﻿#include "DbHandler.h"
#include <QFile>
#include <QEventLoop>

DatabaseWorker::DatabaseWorker(QObject *parent) : QObject(parent)
{
    qRegisterMetaType<QList<QSqlRecord>>("QList<QSqlRecord>");
    qRegisterMetaType<RecordData>("RecordData");
}

DatabaseWorker::~DatabaseWorker()
{
    closeDatabase();
}

void DatabaseWorker::connectToDatabase(const QString &databaseName)
{
    qDebug() << "当前线程ID:" << QThread::currentThreadId();

    if (!QFile::exists(databaseName)) {
        qDebug() << "数据库文件不存在:" << databaseName;
        emit operationResult(false, "数据库文件不存在:" + databaseName);
        return;
    }

    if (!QSqlDatabase::contains("Auto_TCal_DB")) {
        db = QSqlDatabase::addDatabase("QSQLITE", "Auto_TCal_DB");
    } else {
        db = QSqlDatabase::database("Auto_TCal_DB");
    }

    db.setDatabaseName(databaseName);

    if (!db.open()) {
        qDebug() << "无法连接到数据库:" << db.lastError().text();
        emit operationResult(false, "无法连接到数据库:" + db.lastError().text());
        return;
    }

    db.exec("PRAGMA foreign_keys = ON;"); //启用外键约束

    qDebug() << "成功连接数据库";
    emit operationResult(true, "成功连接数据库");
}

void DatabaseWorker::closeDatabase()
{
    if (db.isOpen()) {

        QString connectionName = db.connectionName();
        db.close();
        db = QSqlDatabase();
        QSqlDatabase::removeDatabase(connectionName);
        qDebug() << "数据库连接已关闭：" << connectionName;
        emit operationResult(true, "数据库连接已关闭");
    }
}

bool DatabaseWorker::insertRecord(const QString &tableName, const RecordData &data)
{
    if (!db.isOpen()) {
        emit operationResult(false, "数据库未打开");
        return false;
    }

    QStringList fields;
    QStringList valuesPlaceholders;
    for (auto it = data.cbegin(); it != data.cend(); ++it) {
        fields << it.key();
        valuesPlaceholders << QString(":%1").arg(it.key());
    }

    QString sqlQuery = QString("INSERT INTO %1 (%2) VALUES (%3)")
                           .arg(tableName)
                           .arg(fields.join(", "))
                           .arg(valuesPlaceholders.join(", "));

    QSqlQuery query(db);
    query.prepare(sqlQuery);

    for (auto it = data.cbegin(); it != data.cend(); ++it) {
        query.bindValue(QString(":%1").arg(it.key()), it.value());
    }

    if (!query.exec()) {
        // 输出详细的 SQL 错误信息
        qDebug() << "SQL 插入错误：" << query.lastError().text();
        qDebug() << "执行的 SQL 语句：" << query.lastQuery();
        emit operationResult(false, query.lastError().text());
        return false;
    }

    emit operationResult(true, "插入数据成功");
    return true;
}

bool DatabaseWorker::updateRecord(const QString &tableName, const QString &condition, const RecordData &data)
{
    if (!db.isOpen()) {
        emit operationResult(false, "数据库未打开");
        return false;
    }

    QStringList updateClauses;
    for (auto it = data.cbegin(); it != data.cend(); ++it) {
        updateClauses << QString("%1 = :%1").arg(it.key());
    }

    QString sqlQuery = QString("UPDATE %1 SET %2 WHERE %3")
                           .arg(tableName)
                           .arg(updateClauses.join(", "))
                           .arg(condition);

    QSqlQuery query(db);
    query.prepare(sqlQuery);

    for (auto it = data.cbegin(); it != data.cend(); ++it) {
        query.bindValue(QString(":%1").arg(it.key()), it.value());
    }

    if (!query.exec()) {
        emit operationResult(false, query.lastError().text());
        return false;
    }

    emit operationResult(true, "更新数据成功");
    return true;
}

bool DatabaseWorker::deleteRecord(const QString &tableName, const QString &condition)
{
    if (!db.isOpen()) {
        emit operationResult(false, "数据库未打开");
        return false;
    }

    QString sqlQuery = QString("DELETE FROM %1 WHERE %2").arg(tableName).arg(condition);

    QSqlQuery query(db);
    query.prepare(sqlQuery);

    if (!query.exec()) {
        emit operationResult(false, query.lastError().text());
        return false;
    }

    emit operationResult(true, "删除数据成功");
    return true;
}

bool DatabaseWorker::clearAllRecord(const QString &tableName, const QString &condition)
{
    if (!db.isOpen()) {
        emit operationResult(false, "数据库未打开");
        return false;
    }

    QString sqlQuery = QString("DELETE FROM %1 WHERE %2").arg(tableName).arg(condition);

    QSqlQuery query(db);
    query.prepare(sqlQuery);

    if (!query.exec()) {
        emit operationResult(false, query.lastError().text());
        return false;
    }

    emit operationResult(true, "清除全部数据成功");
    return true;
}

bool DatabaseWorker::queryRecords(const QString &tableName, const QString &condition)
{
    if (!db.isOpen()) {
        emit operationResult(false, "数据库未打开");
        return false;
    }

    // 验证表名
    if (!db.tables().contains(tableName)) {
        emit operationResult(false, QString("表'%1'不存在").arg(tableName));
        return false;
    }

    // 使用QSqlQuery的参数绑定而不是直接拼接SQL
    QSqlQuery query(db);
    if (condition.isEmpty()) {
        query.prepare(QString("SELECT * FROM %1").arg(tableName));
    } else {
        // 注意：这种方法仍然可能受到SQL注入攻击，因为条件是直接拼接的
        // 理想情况下，条件应该使用参数化查询，但这需要重新设计API
        query.prepare(QString("SELECT * FROM %1 WHERE %2").arg(tableName).arg(condition));
    }

    QList<QSqlRecord> records;
    if (!query.exec()) {
        emit recordsQueried(false, records);
        return false;
    }

    while (query.next()) {
        records.append(query.record());
    }

    if (records.isEmpty()) {
        emit recordsQueried(false, records);
    } else {
        emit recordsQueried(true, records);
    }
    return true;
}

bool DatabaseWorker::queryRecordsPro(const QString &sqlQuery)
{
    if (!db.isOpen()) {
        emit operationResult(false, "数据库未打开");
        return false;
    }

    QSqlQuery query(db);
    QList<QSqlRecord> records;

    query.prepare(sqlQuery);

    if (!query.exec()) {
        emit recordsQueried(false, records);
        return false;
    }

    while (query.next()) {
        records.append(query.record());
    }

    emit recordsQueried(true, records);
    return true;
}

void DatabaseWorker::getModel(const QString &tableName)
{
    if (!db.isOpen()) {
        emit operationResult(false, "数据库未打开");
        return;
    }

    QSqlTableModel *model = new QSqlTableModel(nullptr, db);
    model->setTable(tableName);
    model->select();

    emit modelReady(model);
}

void DatabaseWorker::beginTransaction()
{
    if (db.isOpen()) {
        db.transaction();
        emit operationResult(true, "事务开始");
    } else {
        emit operationResult(false, "数据库未打开，无法开始事务");
    }
}

void DatabaseWorker::commitTransaction()
{
    if (db.isOpen()) {
        if (db.commit()) {
            emit operationResult(true, "事务提交成功");
        } else {
            emit operationResult(false, "事务提交失败");
        }
    } else {
        emit operationResult(false, "数据库未打开，无法提交事务");
    }
}

void DatabaseWorker::rollbackTransaction()
{
    if (db.isOpen()) {
        if (db.rollback()) {
            emit operationResult(true, "事务回滚成功");
        } else {
            emit operationResult(false, "事务回滚失败");
        }
    } else {
        emit operationResult(false, "数据库未打开，无法回滚事务");
    }
}

bool DatabaseWorker::isDbOpen() const {
    return db.isOpen();
}

DbHandler::DbHandler(QObject *parent) : QObject(parent), m_model(nullptr)
{
    m_worker = new DatabaseWorker;
    m_thread = new QThread(this);
    m_worker->moveToThread(m_thread);

    connect(this, &DbHandler::connectToDatabaseSignal, m_worker, &DatabaseWorker::connectToDatabase);
    connect(this, &DbHandler::closeDatabaseSignal, m_worker, &DatabaseWorker::closeDatabase);
    connect(this, &DbHandler::insertRecordSignal, m_worker, &DatabaseWorker::insertRecord);
    connect(this, &DbHandler::updateRecordSignal, m_worker, &DatabaseWorker::updateRecord);
    connect(this, &DbHandler::deleteRecordSignal, m_worker, &DatabaseWorker::deleteRecord);
    connect(this, &DbHandler::clearAllRecordSignal, m_worker, &DatabaseWorker::clearAllRecord);
    connect(this, &DbHandler::queryRecordsSignal, m_worker, &DatabaseWorker::queryRecords);
    connect(this, &DbHandler::queryRecordsProSignal, m_worker, &DatabaseWorker::queryRecordsPro);
    connect(this, &DbHandler::getModelSignal, m_worker, &DatabaseWorker::getModel);

    connect(this, &DbHandler::beginTransactionSignal, m_worker, &DatabaseWorker::beginTransaction);
    connect(this, &DbHandler::commitTransactionSignal, m_worker, &DatabaseWorker::commitTransaction);
    connect(this, &DbHandler::rollbackTransactionSignal, m_worker, &DatabaseWorker::rollbackTransaction);

    connect(m_worker, &DatabaseWorker::operationResult, this, &DbHandler::onOperationResult);
    connect(m_worker, &DatabaseWorker::recordsQueried, this, &DbHandler::onRecordsQueried);
    connect(m_worker, &DatabaseWorker::modelReady, this, &DbHandler::onModelReady);

    m_thread->start();
}

DbHandler::~DbHandler()
{
    if (m_thread->isRunning()) {
        m_thread->quit();
        if (!m_thread->wait(3000)) {  // 等待最多3秒
            m_thread->terminate();    // 如果线程没有正常退出，则强制终止
            m_thread->wait();         // 再次等待线程结束
        }
    }
    delete m_worker;  // 现在可以安全删除worker
    delete m_model;
}

bool DbHandler::isDatabaseConnected() const
{
    return m_worker->isDbOpen();
}

bool DbHandler::connectToDatabase(const QString &databaseName)
{
    QMutexLocker locker(&m_mutex);
    emit connectToDatabaseSignal(databaseName);

    QEventLoop loop;
    connect(this, &DbHandler::operationResult, &loop, &QEventLoop::quit);
    loop.exec();

    return m_operationResult;
}

void DbHandler::closeDatabase()
{
    emit closeDatabaseSignal();
}

bool DbHandler::insertRecord(const QString &tableName, const RecordData &data)
{
    QMutexLocker locker(&m_mutex);
    emit insertRecordSignal(tableName, data);

    QEventLoop loop;
    connect(this, &DbHandler::operationResult, &loop, &QEventLoop::quit);
    loop.exec();

    return m_operationResult;
}

bool DbHandler::updateRecord(const QString &tableName, const QString &condition, const RecordData &data)
{
    QMutexLocker locker(&m_mutex);
    emit updateRecordSignal(tableName, condition, data);

    QEventLoop loop;
    connect(this, &DbHandler::operationResult, &loop, &QEventLoop::quit);
    loop.exec();

    return m_operationResult;
}

bool DbHandler::deleteRecord(const QString &tableName, const QString &condition)
{
    QMutexLocker locker(&m_mutex);
    emit deleteRecordSignal(tableName, condition);

    QEventLoop loop;
    connect(this, &DbHandler::operationResult, &loop, &QEventLoop::quit);
    loop.exec();

    return m_operationResult;
}

bool DbHandler::clearAllRecord(const QString &tableName, const QString &condition)
{
    QMutexLocker locker(&m_mutex);
    emit clearAllRecordSignal(tableName, condition);

    QEventLoop loop;
    connect(this, &DbHandler::operationResult, &loop, &QEventLoop::quit);
    loop.exec();

    return m_operationResult;
}

QPair<bool, QList<QSqlRecord>> DbHandler::queryRecords(const QString &tableName, const QString &condition)
{
    QMutexLocker locker(&m_mutex);
    emit queryRecordsSignal(tableName, condition);

    QEventLoop loop;
    connect(this, &DbHandler::recordsQueried, &loop, &QEventLoop::quit);
    loop.exec();

    return qMakePair(!m_queryResult.isEmpty(), m_queryResult);
}

QPair<bool, QList<QSqlRecord>> DbHandler::queryRecordsPro(const QString &sqlQuery)
{
    QMutexLocker locker(&m_mutex);
    emit queryRecordsProSignal(sqlQuery);

    QEventLoop loop;
    connect(this, &DbHandler::recordsQueried, &loop, &QEventLoop::quit);
    loop.exec();

    return qMakePair(!m_queryResult.isEmpty(), m_queryResult);
}

QPair<bool, QSqlTableModel*> DbHandler::getModel(const QString &tableName)
{
    QMutexLocker locker(&m_mutex);
    emit getModelSignal(tableName);

    QEventLoop loop;
    connect(this, &DbHandler::modelReady, &loop, &QEventLoop::quit);
    loop.exec();

    return qMakePair(m_model != nullptr, m_model);
}

// 获取单个值
QVariant DbHandler::getScalarValue(const QString &sqlQuery)
{
    auto result = queryRecordsPro(sqlQuery);
    if (result.first && !result.second.isEmpty()) {
        return result.second.first().value(0);
    }
    return QVariant();
}

// 获取单列值
QList<QVariant> DbHandler::getColumnValues(const QString &sqlQuery)
{
    QList<QVariant> values;
    auto result = queryRecordsPro(sqlQuery);
    if (result.first) {
        for (const auto &record : result.second) {
            values.append(record.value(0));
        }
    }
    return values;
}


bool DbHandler::beginTransaction()
{
    QMutexLocker locker(&m_mutex);
    emit beginTransactionSignal();

    QEventLoop loop;
    connect(this, &DbHandler::operationResult, &loop, &QEventLoop::quit);
    loop.exec();

    return m_operationResult;
}

bool DbHandler::commitTransaction()
{
    QMutexLocker locker(&m_mutex);
    emit commitTransactionSignal();

    QEventLoop loop;
    connect(this, &DbHandler::operationResult, &loop, &QEventLoop::quit);
    loop.exec();

    return m_operationResult;
}

bool DbHandler::rollbackTransaction()
{
    QMutexLocker locker(&m_mutex);
    emit rollbackTransactionSignal();

    QEventLoop loop;
    connect(this, &DbHandler::operationResult, &loop, &QEventLoop::quit);
    loop.exec();

    return m_operationResult;
}


// 接收子线程worker的执行结果便于handler即刻返回结果 如m_operationResult
void DbHandler::onOperationResult(bool success, const QString &message)
{
    m_operationResult = success;
    emit operationResult(success, message);
}

void DbHandler::onRecordsQueried(bool success, const QList<QSqlRecord> &records)
{
    qDebug() << "DbHandler::onRecordsQueried";
    m_queryResult = records;
    emit recordsQueried(success, records);
}

void DbHandler::onModelReady(QSqlTableModel* model)
{
    delete m_model;  // 删除旧的模型（如果存在）
    m_model = model;
    emit modelReady(model);
}
