﻿// #ifndef SERIALHANDLER_H
// #define SERIALHANDLER_H

// #include <QObject>
// #include <QSerialPort>
// #include <QMutex>
// #include <QTimer>
// #include <QThread>
// #include <QQueue>
// #include <QEventLoop>

// #include "DataProcessor.h"

// class SerialWorker : public QObject {
//     Q_OBJECT
// public:
//     explicit SerialWorker(QObject* parent = nullptr);
//     ~SerialWorker();

//    bool isPortOpen() const;
//    QByteArray createModbusCommand(const QString& address, const QString& function, const QString& data);
//    void registerProcessor(DataProcessor* processor);

// public slots:
//     void openPort(const QString& portName, int baudRate);
//     void closePort();
//     void writeData(const QByteArray& data, const QString& command);

// signals:
//     void operationResult(bool success, const QString& message);
//     void dataReceived(const QByteArray& data, const QString& command);
//     void sendResultToGui(bool status, const QString& result, const QString& command);

// private slots:
//     void readData();
//     void doDataReciveWork();
//     void processCommand(const QByteArray& data);
//     void startReceiveTimer();
//     void onTimeout();

// private:
//     QSerialPort* m_serialPort;
//     QTimer* m_receiveDelayTimer;
//     QByteArray m_receivedData;
//     QMutex m_mutex;
//     QString m_currentCommand;
//     bool isSending;
//     QList<DataProcessor*> m_processors;  // 改为列表存储处理器
//     QTimer* m_timeoutTimer;       // 超时定时器
//     bool isCommandTimeout;        // 超时标志
// };

// class SerialHandlerPro : public QObject {
//     Q_OBJECT
// public:
//     explicit SerialHandlerPro(QObject* parent = nullptr);
//     ~SerialHandlerPro();

//    bool isPortOpen() const;
//    void openPort(const QString& portName, int baudRate);
//    void closePort();
//    void writeData(const QByteArray& data, const QString& command);
//    void registerProcessor(DataProcessor* processor);
//    void stopContinuousRead();

// signals:
//     void operationResult(bool success, const QString& message);
//     void dataReceived(const QByteArray& data, const QString& command);
//     void openPortSignal(const QString& portName, int baudRate);
//     void closePortSignal();
//     void writeDataSignal(const QByteArray& data, const QString& command);
//     void stopContinuousReadSignal();
//     void onSendResultToGui(bool status, const QString& result, const QString& command);

// private slots:
//     void onOperationResult(bool success, const QString& message);
//     void onDataReceived(const QByteArray& data, const QString& command);
//     void onSendResultToGuiSlots(bool status, const QString& result, const QString& command);

// private:
//     SerialWorker* m_worker;
//     QThread* m_thread;
//     QMutex m_mutexPro;
//     bool m_operationResult;
// };

// #endif // SERIALHANDLER_H

// SerialHandlerPro.h
#pragma once

#include <QObject>
#include <QThread>
#include <QMutex>
#include <QSerialPort>
#include <QPair>

class DataProcessor;
class SerialWorker;

class SerialHandlerPro : public QObject
{
    Q_OBJECT
public:
    explicit SerialHandlerPro(QObject *parent = nullptr);
    ~SerialHandlerPro();

    // 同步方法
    QPair<bool, QString> writeData(const QByteArray &data, const QString &command);
    bool openPort(const QString &portName, int baudRate);
    bool closePort();
    bool isPortOpen() const;
    void registerProcessor(DataProcessor *processor);

signals:
    // 内部信号
    void writeDataSignal(const QByteArray &data, const QString &command);
    void openPortSignal(const QString &portName, int baudRate);
    void closePortSignal();
    void registerProcessorSignal(DataProcessor *processor);

    // 对外信号
    void operationResult(bool success, const QString &message);

    void resultReady();

private slots:
    void onOperationCompleted(bool success, const QString &result, const QString &command);

private:
    SerialWorker *m_worker;
    QThread *m_thread;
    QMutex m_mutex;

    bool m_operationSuccess;
    QString m_operationResult;
    bool m_resultReady = false;
};
