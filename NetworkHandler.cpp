﻿// NetworkHandler.cpp
#include "NetworkHandler.h"
#include "NetworkWorker.h"
#include "DataProcessor.h"
#include <QEventLoop>

NetworkHandler::NetworkHandler(QObject *parent) : QObject(parent),
                                                  m_resultReady(false)
{
    m_worker = new NetworkWorker;
    m_thread = new QThread(this);
    m_worker->moveToThread(m_thread);

    // 建立线程与worker的生命周期关系
    connect(m_thread, &QThread::finished, m_worker, &QObject::deleteLater);

    connect(this, &NetworkHandler::writeDataSignal, m_worker, &NetworkWorker::writeData);
    connect(this, &NetworkHandler::connectToHostSignal, m_worker, &NetworkWorker::connectToHost);
    connect(this, &NetworkHandler::disconnectFromHostSignal, m_worker, &NetworkWorker::disconnectFromHost);

    connect(m_worker, &NetworkWorker::operationCompleted,
            this, &NetworkHandler::onOperationCompleted);

    m_thread->start();
}

NetworkHandler::~NetworkHandler()
{
    // emit disconnectFromHostSignal();
    disconnectFromHost();

    if (m_thread)
    {
        m_thread->quit();
        if (!m_thread->wait(500))
        {
            m_thread->terminate();
            m_thread->wait();
        }
    }

    m_worker = nullptr;
}

// QPair<bool, QString> NetworkHandler::writeData(const QByteArray &data, const QString &command)
//{
//     QMutexLocker locker(&m_mutex);
//     emit writeDataSignal(data, command);

//    QEventLoop loop;
//    connect(m_worker, &NetworkWorker::operationCompleted, &loop, &QEventLoop::quit);
//    loop.exec();

//    return qMakePair(m_operationSuccess, m_operationResult);
//}

QPair<bool, QString> NetworkHandler::writeData(const QByteArray &data, const QString &command)
{
    QMutexLocker locker(&m_mutex);

    // Reset result ready flag
    m_resultReady = false;

    emit writeDataSignal(data, command);

    QEventLoop loop;

    // Use our own resultReady signal instead of worker's operationCompleted
    connect(this, &NetworkHandler::resultReady, &loop, &QEventLoop::quit);

    // Set timeout to prevent infinite wait
    QTimer timer;
    timer.setSingleShot(true);
    connect(&timer, &QTimer::timeout, &loop, &QEventLoop::quit);
    timer.start(6000); // 6秒超时 (比工作线程超时长1秒)

    loop.exec();

    // Check if we timed out
    if (!m_resultReady)
    {
        // return qMakePair(false, "Operation timeout");
        return QPair<bool, QString>(false, "Operation timeout");
    }

    return qMakePair(m_operationSuccess, m_operationResult);
}

// QPair<bool, QString> NetworkHandler::connectToHost(const QString &host, quint16 port)
//{
//     QMutexLocker locker(&m_mutex);
//     emit connectToHostSignal(host, port);

//    QEventLoop loop;
//    connect(m_worker, &NetworkWorker::operationCompleted, &loop, &QEventLoop::quit);
//    loop.exec();

//    return qMakePair(m_operationSuccess, m_operationResult);
//}

// bool NetworkHandler::disconnectFromHost()
//{
//     QMutexLocker locker(&m_mutex);
//     emit disconnectFromHostSignal();

//    QEventLoop loop;
//    connect(m_worker, &NetworkWorker::operationCompleted, &loop, &QEventLoop::quit);
//    loop.exec();

//    return m_operationSuccess;
//}

QPair<bool, QString> NetworkHandler::connectToHost(const QString &host, quint16 port)
{
    QMutexLocker locker(&m_mutex);

    // Reset result ready flag
    m_resultReady = false;

    emit connectToHostSignal(host, port);

    QEventLoop loop;

    // Use our own resultReady signal
    connect(this, &NetworkHandler::resultReady, &loop, &QEventLoop::quit);

    // Set timeout to prevent infinite wait
    QTimer timer;
    timer.setSingleShot(true);
    connect(&timer, &QTimer::timeout, &loop, &QEventLoop::quit);
    timer.start(6000); // 6秒超时

    loop.exec();

    // Check if we timed out
    if (!m_resultReady)
    {
        return QPair<bool, QString>(false, "Operation timeout");
    }

    return qMakePair(m_operationSuccess, m_operationResult);
}

bool NetworkHandler::disconnectFromHost()
{
    QMutexLocker locker(&m_mutex);

    // Reset result ready flag
    m_resultReady = false;

    emit disconnectFromHostSignal();

    QEventLoop loop;

    // Use our own resultReady signal
    connect(this, &NetworkHandler::resultReady, &loop, &QEventLoop::quit);

    // Set timeout to prevent infinite wait
    QTimer timer;
    timer.setSingleShot(true);
    connect(&timer, &QTimer::timeout, &loop, &QEventLoop::quit);
    timer.start(6000); // 6秒超时

    loop.exec();

    return m_operationSuccess;
}

bool NetworkHandler::isConnected() const
{
    return m_worker->isConnected();
}

void NetworkHandler::registerProcessor(DataProcessor *processor)
{
    QMetaObject::invokeMethod(m_worker, [this, processor]()
                              { m_worker->registerProcessor(processor); }, Qt::BlockingQueuedConnection);
}

// void NetworkHandler::onOperationCompleted(bool success, const QString &result, const QString &command)
//{
//     m_operationSuccess = success;
//     m_operationResult = result;
//     emit operationResult(success, result);
// }
void NetworkHandler::onOperationCompleted(bool success, const QString &result, const QString &command)
{
    m_operationSuccess = success;
    m_operationResult = result;
    m_resultReady = true; // 标记结果已就绪

    emit operationResult(success, result);
    emit resultReady(); // 发送信号通知事件循环退出
}

// #include "NetworkHandler.h"
// #include <QDebug>
// NetworkWorker::NetworkWorker(QObject *parent)
//     : QObject(parent)
//     , m_socket(new QTcpSocket(this))
//     , m_receiveDelayTimer(new QTimer(this))
//     , isSending(false)
//     , m_timeoutTimer(new QTimer(this))
//{
//     connect(m_socket, &QTcpSocket::connected, this, &NetworkWorker::handleConnected);
//     connect(m_socket, &QTcpSocket::disconnected, this, &NetworkWorker::handleDisconnected);
//     connect(m_socket, &QTcpSocket::readyRead, this, &NetworkWorker::startReceiveTimer);
//     connect(m_socket, QOverload<QAbstractSocket::SocketError>::of(&QTcpSocket::errorOccurred),
//             this, &NetworkWorker::handleError);

//    m_receiveDelayTimer->setSingleShot(true);
//    connect(m_receiveDelayTimer, &QTimer::timeout, this, &NetworkWorker::doDataReciveWork);

//    m_timeoutTimer->setSingleShot(true);
//    connect(m_timeoutTimer, &QTimer::timeout, this, &NetworkWorker::onTimeout);

//    // 注册默认处理器
//    registerProcessor(new ZCLOG334NTCProcessor(this));
//}

// NetworkWorker::~NetworkWorker()
//{
//     if (m_socket->state() == QAbstractSocket::ConnectedState) {
//         m_socket->disconnectFromHost();
//     }

//    qDeleteAll(m_processors);
//    delete m_socket;
//}

// void NetworkWorker::registerProcessor(DataProcessor* processor) {
//     QMutexLocker locker(&m_mutex);
//     if (!m_processors.contains(processor)) {
//         m_processors.append(processor);
//         connect(processor, &DataProcessor::resultReady, this, &NetworkWorker::sendResultToGui);
//     }
// }

// bool NetworkWorker::isConnected() const
//{
//     return m_socket->state() == QAbstractSocket::ConnectedState;
// }

// void NetworkWorker::connectToHost(const QString &host, quint16 port)
//{
//     QMutexLocker locker(&m_mutex);
//     if (m_socket->state() == QAbstractSocket::ConnectedState) {
//         m_socket->disconnectFromHost();
//     }
//     m_socket->connectToHost(host, port);
// }

// void NetworkWorker::disconnectFromHost()
//{
//     QMutexLocker locker(&m_mutex);
//     if (m_socket->state() == QAbstractSocket::ConnectedState) {
//         m_socket->disconnectFromHost();
//     }
// }

// void NetworkWorker::writeData(const QByteArray &data, const QString &command)
//{
//     QMutexLocker locker(&m_mutex);

//    if (isSending) {
//        QThread::msleep(500);
//    }

//    isSending = true;
//    m_currentCommand = command;

//    //qDebug() << "Network thread sending data:" << data.toHex() << "Thread ID:" << QThread::currentThreadId();
//    m_socket->write(data);
//    m_socket->flush();

//    isCommandTimeout = false;        // 重置超时标志
//    m_timeoutTimer->start(5000);
//    isSending = false;

//}

// void NetworkWorker::handleConnected()
//{
//     emit operationResult(true, "网络连接成功");
// }

// void NetworkWorker::handleDisconnected()
//{
//     emit operationResult(true, "网络已断开");
// }

// void NetworkWorker::handleError(QAbstractSocket::SocketError socketError)
//{
//     QString errorMessage = "网络错误: " + m_socket->errorString();
//                                                   emit operationResult(false, errorMessage);
// }

// void NetworkWorker::startReceiveTimer()
//{
//     m_receivedData.append(m_socket->readAll());
//     if (!m_receiveDelayTimer->isActive()) {
//         m_receiveDelayTimer->start(100);
//     }
// }

// void NetworkWorker::doDataReciveWork()
//{
//     QMutexLocker locker(&m_mutex);
//     processCommand(m_receivedData);
// }

// void NetworkWorker::processCommand(const QByteArray &data)
//{
//     if (!m_currentCommand.isEmpty() && !isCommandTimeout) {
//         // 处理命令
//         for (DataProcessor* processor : m_processors) {
//             if (processor->supportsCommand(m_currentCommand)) {
//                 processor->processData(data, m_currentCommand);
//                 if (m_timeoutTimer->isActive()) {
//                     m_timeoutTimer->stop(); // Stop timer since data was processed
//                 }
//             } else {
//                 emit sendResultToGui(false, "", m_currentCommand);
//             }
//         }
//         // 处理完成后清理数据
//         m_socket->flush();
//         m_receivedData.clear();
//         m_currentCommand.clear();
//     } else if (isCommandTimeout) {
//         // 忽略超时命令的迟到数据
//         qDebug() << "忽略超时命令的迟到响应";
//     }
// }

// void NetworkWorker::onTimeout() {
//     QMutexLocker locker(&m_mutex);
//     if (!m_currentCommand.isEmpty()) {
//         isCommandTimeout = true;
//         emit sendResultToGui(false, "timeout", m_currentCommand);
//         m_currentCommand.clear();
//         m_socket->flush();
//         m_receivedData.clear();
//     }
// }

//// NetworkHandler implementation
// NetworkHandler::NetworkHandler(QObject *parent) : QObject(parent)
//{
//     m_worker = new NetworkWorker;
//     m_thread = new QThread(this);

//    m_worker->moveToThread(m_thread);

//    connect(this, &NetworkHandler::connectToHostSignal, m_worker, &NetworkWorker::connectToHost);
//    connect(this, &NetworkHandler::disconnectFromHostSignal, m_worker, &NetworkWorker::disconnectFromHost);
//    connect(this, &NetworkHandler::writeDataSignal, m_worker, &NetworkWorker::writeData);

//    connect(m_worker, &NetworkWorker::operationResult, this, &NetworkHandler::onOperationResult);
//    connect(m_worker, &NetworkWorker::sendResultToGui, this, &NetworkHandler::onSendResultToGuiSlots);

//    connect(m_thread, &QThread::finished, m_worker, &QObject::deleteLater);

//    m_thread->start();
//}

// NetworkHandler::~NetworkHandler()
//{
//     emit disconnectFromHostSignal();

//    m_thread->quit();
//    m_thread->wait();

//    delete m_thread;
//    //m_worker->deleteLater();
//}

// void NetworkHandler::registerProcessor(DataProcessor* processor) {
//     QMetaObject::invokeMethod(m_worker, [this, processor]() {
//         m_worker->registerProcessor(processor);
//     });
// }

// bool NetworkHandler::isConnected() const
//{
//     return m_worker->isConnected();
// }

// void NetworkHandler::connectToHost(const QString &host, quint16 port)
//{
//     emit connectToHostSignal(host, port);
// }

// void NetworkHandler::disconnectFromHost()
//{
//     emit disconnectFromHostSignal();
// }

// void NetworkHandler::writeData(const QByteArray &data, const QString &command)
//{
//     emit writeDataSignal(data, command);
// }

// void NetworkHandler::onOperationResult(bool success, const QString &message)
//{
//     emit operationResult(success, message);
// }

// void NetworkHandler::onSendResultToGuiSlots(bool status, const QString &result, const QString &isThis)
//{
//     emit onSendResultToGui(status, result, isThis);
// }
