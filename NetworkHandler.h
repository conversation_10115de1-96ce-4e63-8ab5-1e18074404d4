﻿// NetworkHandler.h
#pragma once

#include <QObject>
#include <QThread>
#include <QMutex>
#include <QPair>

class DataProcessor;
class NetworkWorker;

class NetworkHandler : public QObject
{
    Q_OBJECT
public:
    explicit NetworkHandler(QObject *parent = nullptr);
    ~NetworkHandler();

    // 同步方法
    QPair<bool, QString> writeData(const QByteArray &data, const QString &command);
    QPair<bool, QString> connectToHost(const QString &host, quint16 port);
    bool disconnectFromHost();
    bool isConnected() const;
    void registerProcessor(DataProcessor *processor);

signals:
    // 内部信号
    void writeDataSignal(const QByteArray &data, const QString &command);
    void connectToHostSignal(const QString &host, quint16 port);
    void disconnectFromHostSignal();
    void registerProcessorSignal(DataProcessor *processor);

    // 对外信号
    void operationResult(bool success, const QString &message);

    void resultReady(); // 新增: 在结果准备好后触发

private slots:
    void onOperationCompleted(bool success, const QString &result, const QString &command);

private:
    NetworkWorker *m_worker;
    QThread *m_thread;
    QMutex m_mutex;

    bool m_operationSuccess;
    QString m_operationResult;

    bool m_resultReady; // 新增: 标记结果是否已更新
};

// #ifndef NETWORKHANDLER_H
// #define NETWORKHANDLER_H

// #include <QObject>
// #include <QTcpSocket>
// #include <QThread>
// #include <QMutex>
// #include <QTimer>
// #include <QQueue>
// #include <QEventLoop>
// #include "DataProcessor.h"

// class NetworkWorker : public QObject
//{
//     Q_OBJECT
// public:
//     explicit NetworkWorker(QObject *parent = nullptr);
//     ~NetworkWorker();
//     bool isConnected() const;
//     void registerProcessor(DataProcessor* processor);

// public slots:
//     void connectToHost(const QString &host, quint16 port);
//     void disconnectFromHost();
//     void writeData(const QByteArray &data, const QString &command);

// signals:
//     void operationResult(bool success, const QString &message);
//     void sendResultToGui(bool status, const QString &result, const QString &isThis);

// private slots:
//     void handleConnected();
//     void handleDisconnected();
//     void handleError(QAbstractSocket::SocketError socketError);
//     void startReceiveTimer();
//     void doDataReciveWork();
//     void onTimeout();
// private:
//     QTcpSocket *m_socket;
//     QMutex m_mutex;
//     QTimer *m_receiveDelayTimer;
//     QByteArray m_receivedData;
//     bool isSending;
//     QString m_currentCommand;
//     void processCommand(const QByteArray &data);
//     QList<DataProcessor*> m_processors;
//     QTimer* m_timeoutTimer;       // 超时定时器
//     bool isCommandTimeout;        // 超时标志

//};

// class NetworkHandler : public QObject
//{
//     Q_OBJECT
// public:
//     explicit NetworkHandler(QObject *parent = nullptr);
//     ~NetworkHandler();
//     bool isConnected() const;
//     void registerProcessor(DataProcessor* processor);

// public slots:
//     void connectToHost(const QString &host, quint16 port);
//     void disconnectFromHost();
//     void writeData(const QByteArray &data, const QString &command);

// signals:
//     void connectToHostSignal(const QString &host, quint16 port);
//     void disconnectFromHostSignal();
//     void writeDataSignal(const QByteArray &data, const QString &command);
//     void operationResult(bool success, const QString &message);
//     void onSendResultToGui(bool status, const QString &result, const QString &isThis);

// private slots:
//     void onOperationResult(bool success, const QString &message);
//     void onSendResultToGuiSlots(bool status, const QString &result, const QString &isThis);

// private:
//     NetworkWorker *m_worker;
//     QThread *m_thread;
//     QMutex m_mutexPro;
//     bool m_operationResult;

//};

// #endif // NETWORKHANDLER_H
