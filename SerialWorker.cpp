﻿// SerialWorker.cpp
#include "SerialWorker.h"
#include "DataProcessor.h"

SerialWorker::SerialWorker(QObject *parent) : QObject(parent),
                                              m_isTimeout(false),
                                              m_isWaitingResponse(false)
{
    m_serialPort = new QSerialPort(this);
    m_receiveTimer = new QTimer(this);
    m_timeoutTimer = new QTimer(this);

    m_receiveTimer->setSingleShot(true);
    m_timeoutTimer->setSingleShot(true);

    connect(m_serialPort, &QSerialPort::readyRead, this, &SerialWorker::handleReadyRead);
    connect(m_receiveTimer, &QTimer::timeout, this, &SerialWorker::processReceivedData);
    connect(m_timeoutTimer, &QTimer::timeout, this, &SerialWorker::handleTimeout);
}

SerialWorker::~SerialWorker()
{
    closePort();

    m_processors.clear();

    // DataProcessor对象(如m_calDeviceProcessor_Serial)是通过new calDeviceProcessor(this)创建的，这给它们设置了MainWindow作为父对象
    // 这些相同的处理器又被注册到SerialWorker的m_processors容器中
    // 当程序退出时，发生了双重删除：
    // MainWindow析构时会删除其子对象(包括处理器)
    // SerialWorker析构时通过qDeleteAll(m_processors)再次尝试删除这些处理器
    // qDeleteAll(m_processors);
}

void SerialWorker::openPort(const QString &portName, int baudRate)
{
    QMutexLocker locker(&m_mutex);

    if (m_serialPort->isOpen())
    {
        m_serialPort->close();
    }

    m_serialPort->setPortName(portName);
    m_serialPort->setBaudRate(baudRate);
    m_serialPort->setDataBits(QSerialPort::Data8);
    m_serialPort->setParity(QSerialPort::NoParity);
    m_serialPort->setStopBits(QSerialPort::OneStop);
    m_serialPort->setFlowControl(QSerialPort::NoFlowControl);

    bool success = m_serialPort->open(QIODevice::ReadWrite);
    QString message = success ? "串口打开成功" : "串口打开失败: " + m_serialPort->errorString();

    emit operationCompleted(success, message, "openPort");
}

void SerialWorker::closePort()
{
    QMutexLocker locker(&m_mutex);

    if (m_serialPort->isOpen())
    {
        m_serialPort->close();
    }
    emit operationCompleted(true, "串口已关闭", "closePort");
}

// void SerialWorker::writeData(const QByteArray &data, const QString &command)
//{
//     QMutexLocker locker(&m_mutex);

//    if(!m_serialPort->isOpen()) {
//        emit operationCompleted(false, "串口未打开", command);
//        return;
//    }

//    clearBuffers();
//    m_currentCommand = command;
//    m_isTimeout = false;

//    if(m_serialPort->write(data) == data.size()) {
//        m_timeoutTimer->start(5000); // 5秒超时
//    } else {
//        emit operationCompleted(false, "数据发送失败", command);
//    }
//}

void SerialWorker::writeData(const QByteArray &data, const QString &command)
{
    QMutexLocker locker(&m_mutex);

    if (!m_serialPort->isOpen())
    {
        emit operationCompleted(false, "串口未打开", command);
        return;
    }

    clearBuffers();
    stopTimers(); // 新增方法，停止所有定时器

    m_currentCommand = command;
    m_isTimeout = false;
    m_isWaitingResponse = true; // 新增状态变量，标记正在等待响应

    if (m_serialPort->write(data) == data.size())
    {
        m_timeoutTimer->start(5000); // 5秒超时
    }
    else
    {
        m_isWaitingResponse = false;
        emit operationCompleted(false, "数据发送失败", command);
    }
}

// void SerialWorker::handleReadyRead()
//{
//     if(!m_receiveTimer->isActive()) {
//         m_receiveTimer->start(100); // 100ms 接收延时
//     }
//     m_receivedData.append(m_serialPort->readAll());
// }

// void SerialWorker::processReceivedData()
//{
//     QMutexLocker locker(&m_mutex);

//    if(m_isTimeout || m_currentCommand.isEmpty()) {
//        return;
//    }

//    for(DataProcessor* processor : m_processors) {
//        if(processor->supportsCommand(m_currentCommand)) {
//            processor->processData(m_receivedData, m_currentCommand);
//            clearBuffers();
//            return;
//        }
//    }
//}

// void SerialWorker::handleTimeout()
//{
//     QMutexLocker locker(&m_mutex);
//     m_isTimeout = true;
//     emit operationCompleted(false, "操作超时", m_currentCommand);
//     clearBuffers();
// }

void SerialWorker::handleReadyRead()
{
    if (!m_isWaitingResponse || m_isTimeout)
    {
        // 不在等待响应状态或已超时，忽略数据
        m_serialPort->readAll(); // 清空串口缓冲区但不处理
        return;
    }

    if (!m_receiveTimer->isActive())
    {
        m_receiveTimer->start(100); // 100ms 接收延时
    }
    m_receivedData.append(m_serialPort->readAll());
}

void SerialWorker::processReceivedData()
{
    QMutexLocker locker(&m_mutex);

    if (m_isTimeout || !m_isWaitingResponse || m_currentCommand.isEmpty())
    {
        return;
    }

    bool processed = false;
    for (DataProcessor *processor : m_processors)
    {
        if (processor->supportsCommand(m_currentCommand))
        {
            // Process the data - this will eventually emit resultReady which triggers operationCompleted
            processor->processData(m_receivedData, m_currentCommand);
            processed = true;
            break;
        }
    }

    // Only reset state after processing is complete
    m_isWaitingResponse = false;
    stopTimers();
    clearBuffers();

    // If no processor handled it, emit operation completed here
    if (!processed)
    {
        emit operationCompleted(false, "找不到匹配的处理器", m_currentCommand);
    }
}

void SerialWorker::handleTimeout()
{
    QMutexLocker locker(&m_mutex);

    if (!m_isWaitingResponse)
    {
        return; // 如果不是在等待响应状态，忽略超时
    }

    m_isTimeout = true;
    m_isWaitingResponse = false;
    stopTimers(); // 停止所有计时器

    emit operationCompleted(false, "操作超时", m_currentCommand);
    clearBuffers();
}

void SerialWorker::stopTimers()
{
    if (m_receiveTimer->isActive())
    {
        m_receiveTimer->stop();
    }
    if (m_timeoutTimer->isActive())
    {
        m_timeoutTimer->stop();
    }
}

void SerialWorker::clearBuffers()
{
    m_receivedData.clear();
    m_currentCommand.clear();
    m_serialPort->clear();
}

void SerialWorker::registerProcessor(DataProcessor *processor)
{
    QMutexLocker locker(&m_mutex);
    if (!m_processors.contains(processor))
    {
        m_processors.append(processor);
        connect(processor, &DataProcessor::resultReady,
                this, [this](bool success, const QString &result, const QString &command)
                { emit operationCompleted(success, result, command); });
    }
}

bool SerialWorker::isPortOpen() const
{
    return m_serialPort->isOpen();
}
