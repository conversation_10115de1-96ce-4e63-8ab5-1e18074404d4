# 透传模式敏感操作检查扩展

## 问题描述
原始代码只在关闭软件时检查透传模式状态，但对于其他敏感操作（如切换设备型号、打开/关闭串口等）没有进行透传模式检查，可能导致设备状态不一致。

## 解决方案

### 1. 新增通用检查函数
```cpp
bool MainWindow::checkTransparentModeBeforeOperation(const QString &operationName)
{
    if (isWakeUp && !currentTransparentDevice.isEmpty()) {
        QMessageBox::StandardButton reply = QMessageBox::question(
            parentWidget,
            "透传模式提醒",
            QString("设备 %1 当前处于透传模式，建议先退出透传模式再%2。\n\n是否强制继续操作？")
                .arg(currentTransparentDevice).arg(operationName),
            QMessageBox::Yes | QMessageBox::No,
            QMessageBox::No);

        if (reply == QMessageBox::No) {
            return false; // 用户选择不继续操作
        } else {
            // 用户选择强制继续，重置透传状态
            resetTransparentModeState();
        }
    }
    return true; // 允许操作
}
```

### 2. 扩展的敏感操作检查

#### 2.1 设备型号切换
**位置**: `MainWindow::updateChannelNums_Cal()`
**原始行为**: 直接重置透传状态
**新行为**: 提示用户是否继续切换设备型号

```cpp
void MainWindow::updateChannelNums_Cal(const QString &model) {
    if (!checkTransparentModeBeforeOperation("切换设备型号")) {
        return; // 用户选择不继续
    }
    // 继续原有逻辑...
}
```

#### 2.2 设备连接操作
**位置**: `MainWindow::on_connectButton_Cal_clicked()`
**新行为**: 连接前检查透传模式

```cpp
void MainWindow::on_connectButton_Cal_clicked() {
    if (!checkTransparentModeBeforeOperation("连接设备")) {
        return;
    }
    // 继续连接逻辑...
}
```

#### 2.3 设备断开操作
**位置**: `MainWindow::on_disconnectButton_Cal_clicked()`
**新行为**: 断开前检查透传模式

```cpp
void MainWindow::on_disconnectButton_Cal_clicked() {
    if (!checkTransparentModeBeforeOperation("断开设备连接")) {
        return;
    }
    // 继续断开逻辑...
}
```

#### 2.4 通信类型切换
**位置**: `MainWindow::onCommunicationType_CalChanged()`
**新行为**: 切换前检查透传模式，如果用户取消则恢复原选择

```cpp
void MainWindow::onCommunicationType_CalChanged(int index) {
    if (!checkTransparentModeBeforeOperation("切换通信类型")) {
        // 恢复之前的选择
        ui->communicationType_Cal->blockSignals(true);
        ui->communicationType_Cal->setCurrentIndex(index == 0 ? 1 : 0);
        ui->communicationType_Cal->blockSignals(false);
        return;
    }
    // 继续切换逻辑...
}
```

### 3. 用户交互流程

#### 3.1 用户选择"否"（推荐）
1. 显示提醒对话框
2. 用户点击"否"
3. 操作被取消，界面状态恢复
4. 透传模式状态保持不变

#### 3.2 用户选择"是"（强制继续）
1. 显示提醒对话框
2. 用户点击"是"
3. 自动重置透传模式状态
4. 继续执行原操作
5. 记录日志："用户强制[操作名]，自动重置透传模式状态"

### 4. 已有的透传模式检查

#### 4.1 软件关闭检查
**位置**: `MainWindow::closeEvent()`
**行为**: 提示用户是否强制关闭程序

#### 4.2 序列号读写检查
**位置**: 读写序列号相关函数
**行为**: 检查透传状态和设备型号一致性

### 5. 透传模式相关操作总览

| 操作类型 | 检查函数 | 用户选择"否"的行为 | 用户选择"是"的行为 |
|---------|---------|------------------|------------------|
| 关闭软件 | `closeEvent()` | 取消关闭 | 强制关闭 |
| 切换设备型号 | `checkTransparentModeBeforeOperation()` | 取消切换 | 重置状态并切换 |
| 连接设备 | `checkTransparentModeBeforeOperation()` | 取消连接 | 重置状态并连接 |
| 断开设备 | `checkTransparentModeBeforeOperation()` | 取消断开 | 重置状态并断开 |
| 切换通信类型 | `checkTransparentModeBeforeOperation()` | 恢复原选择 | 重置状态并切换 |
| 读写序列号 | 专用检查逻辑 | 取消操作 | 不适用（必须先退出透传） |

### 6. 技术细节

#### 6.1 信号阻塞机制
对于ComboBox等控件的切换操作，使用`blockSignals()`防止递归调用：
```cpp
ui->communicationType_Cal->blockSignals(true);
ui->communicationType_Cal->setCurrentIndex(previousIndex);
ui->communicationType_Cal->blockSignals(false);
```

#### 6.2 父窗口自动检测
`checkTransparentModeBeforeOperation()`会自动检测当前活动的对话框作为父窗口：
- 如果CalibrationDialog可见，使用它作为父窗口
- 如果VerificationDialog可见，使用它作为父窗口
- 否则使用MainWindow作为父窗口

#### 6.3 日志记录
所有透传模式相关的操作都会记录详细日志，便于调试和用户了解状态变化。

## 效果对比

### 修改前
- 只有关闭软件时检查透传模式
- 其他敏感操作直接执行，可能导致设备状态不一致
- 用户可能不知道透传状态已被意外重置

### 修改后
- 所有敏感操作都检查透传模式
- 给用户明确的选择权
- 操作更加安全和可预测
- 减少因误操作导致的设备状态问题

这样的改进大大提高了软件的健壮性和用户体验，避免了因透传模式状态不一致导致的各种问题。
