﻿// SerialWorker.h
#pragma once

#include <QObject>
#include <QSerialPort>
#include <QTimer>
#include <QMutex>
#include <QList>

class DataProcessor;

class SerialWorker : public QObject
{
    Q_OBJECT
public:
    explicit SerialWorker(QObject *parent = nullptr);
    ~SerialWorker();

    bool isPortOpen() const;

public slots:
    void openPort(const QString &portName, int baudRate);
    void closePort();
    void writeData(const QByteArray &data, const QString &command);
    void registerProcessor(DataProcessor *processor);

signals:
    void operationCompleted(bool success, const QString &result, const QString &command);

private slots:
    void handleReadyRead();
    void handleTimeout();
    void processReceivedData();

private:
    QSerialPort *m_serialPort;
    QTimer *m_receiveTimer;
    QTimer *m_timeoutTimer;
    QMutex m_mutex;
    QByteArray m_receivedData;
    QString m_currentCommand;
    bool m_isTimeout;
    QList<DataProcessor *> m_processors;
    void clearBuffers();

    bool m_isWaitingResponse;
    void stopTimers();
};
