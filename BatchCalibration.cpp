#include "batchcalibration.h"
#include <QThread>
#include <QCoreApplication>
#include <QMessageBox>
#include <QApplication>

BatchCalibrationManager::BatchCalibrationManager(QObject *parent)
    : QObject(parent), m_worker(nullptr), m_currentBatchIndex(0),
      m_abortRequested(false), m_batchRetryCount(0)
{
}

BatchCalibrationManager::~BatchCalibrationManager()
{
    m_worker = nullptr;
}

// 用于防止分批标定与复校冲突
void BatchCalibrationManager::cleanupWorker()
{
    if (m_worker)
    {
        m_worker = nullptr;
    }
}

void BatchCalibrationManager::recalibrateChannel(int channelToRecalibrate)
{
    // 停止当前正在进行的批量标定（如果有）
    cleanupWorker();

    // 创建单通道复校配置
    CalDeviceConfig recalConfig = m_deviceConfig; // 复制原始配置

    // 1. 设置为单通道配置
    recalConfig.num_channels = 1;
    recalConfig.switchDelay = m_deviceConfig.switchDelay; // 保持开关延迟配置

    // 2. 存储原始参考阻值 保持不变 因为所有通道共用一个阻值 并且防止复校时qvector越界
    // double originalRefValue = m_deviceConfig.ref_values[channelToRecalibrate];
    // recalConfig.ref_values.clear();
    // recalConfig.ref_values.append(originalRefValue);

    // 3. 调整寄存器地址
    // int registerStride = (m_deviceConfig.name != "VS") ? 4 : 2;
    // recalConfig.write_addr = m_deviceConfig.write_addr + (channelToRecalibrate * registerStride);
    // recalConfig.read_addr = m_deviceConfig.read_addr + (channelToRecalibrate * registerStride);

    // 4. 设置1220映射（使用固定通道，如通道6） 这里尽量保持与原配置一致 同样防止worker在restartCalibration时越界
    // recalConfig.cal_to_1220.clear();
    // recalConfig.cal_to_1220.append(6); // 使用1220上的固定通道
    for (int i = 0; i < recalConfig.cal_to_1220.size(); ++i)
    {
        recalConfig.cal_to_1220[i] = 6; // 将每个元素的值替换为6
    }

    // 提示用户进行物理连接
    QString message = QString("准备复校设备通道 %1:\n\n"
                              "请将设备通道 %1 连接到 1220 的通道 6\n"
                              "请确保参考电阻连接到 1220 的通道 %2")
                          .arg(channelToRecalibrate)
                          .arg(recalConfig.ref_index);

    // 弹出连接提示对话框
    QWidget *parentWidget = QApplication::activeWindow(); // Get the active window as parent
    QMessageBox connectionPrompt(QMessageBox::Information,
                                 "复校连接提示",
                                 message,
                                 QMessageBox::Ok | QMessageBox::Cancel,
                                 parentWidget);

    int result = connectionPrompt.exec();
    if (result != QMessageBox::Ok)
    {
        emit logMessage("复校操作已取消");
        // 应当发射失败信号 让UI界面恢复按钮可用性
        emit allBatchesCompleted(false);
        return;
    }

    // 创建一个临时工作器并执行复校
    m_worker = nullptr;

    m_worker = new CalibrationWorker();
    m_worker->setDeviceConfig(recalConfig);
    m_worker->setCommandHandlers(m_calHandler, m_deviceHandler);

    // 连接信号
    connect(m_worker, &CalibrationWorker::calibrationFinished,
            this, &BatchCalibrationManager::onRecalibrationFinished);
    connect(m_worker, &CalibrationWorker::logMessage,
            this, &BatchCalibrationManager::handleRecalibrationMessages);
    connect(m_worker, &CalibrationWorker::updateChannelData,
            [this](int channel, double resistance, double deviation, bool passed)
            {
                emit updateChannelData(channel, resistance, deviation, passed);
            });

    // 启动复校
    QThread *thread = new QThread();
    m_worker->moveToThread(thread);

    // 使用restartCalibration方法而不是startCalibration
    connect(thread, &QThread::started, [this, channelToRecalibrate]()
            {
        // 使用分批版专用复校方法 而非restartCalibration 或者
        m_worker->restartCalibration(channelToRecalibrate); });

    connect(m_worker, &CalibrationWorker::calibrationFinished, thread, &QThread::quit);
    connect(thread, &QThread::finished, m_worker, &QObject::deleteLater);
    connect(thread, &QThread::finished, thread, &QThread::deleteLater);

    // 记录当前正在复校的通道
    m_recalibratingChannel = channelToRecalibrate;

    // 发送日志
    emit logMessage(QString("开始对设备通道 %1 进行复校").arg(channelToRecalibrate));

    // 启动线程
    thread->start();
}

// 处理复校完成
void BatchCalibrationManager::onRecalibrationFinished(bool success)
{
    emit allBatchesCompleted(success);

    if (success)
    {
        emit logMessage(QString("通道 %1 复校成功").arg(m_recalibratingChannel));

        // // 如果需要，还可以从worker获取结果并更新到m_allResults X
        // if (m_worker) {
        //     QVector<QPair<double, double>> results = m_worker->getCalibrationResults();
        //     if (!results.isEmpty()) {
        //         m_allResults[m_recalibratingChannel] = results[0];
        //     }
        // }
    }
    else
    {
        emit logMessage(QString("通道 %1 复校失败").arg(m_recalibratingChannel));
    }

    // 复校完成后发出信号
    // emit recalibrationCompleted(m_recalibratingChannel, success);

    // 重置状态
    cleanupWorker();
}

// 处理复校消息
void BatchCalibrationManager::handleRecalibrationMessages(const QString &message)
{
    QString prefixedMessage = QString("复校通道 %1: %2")
                                  .arg(m_recalibratingChannel)
                                  .arg(message);
    emit logMessage(prefixedMessage);
}

void BatchCalibrationManager::startBatchCalibration(const CalDeviceConfig &deviceConfig,
                                                    CommandHandlerFunc calHandler,
                                                    CommandHandlerFunc deviceHandler)
{
    m_deviceConfig = deviceConfig;
    m_calHandler = calHandler;
    m_deviceHandler = deviceHandler;
    m_abortRequested = false;
    m_currentBatchIndex = 0;
    m_batchRetryCount = 0;

    // 当设备通道数>16时 前4个用作参考通道
    const int referenceReservedChannels = 4;

    // 16个剩余通道
    const int availableCalChannels = 20 - referenceReservedChannels;

    // Calculate batches 32
    // {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15},
    // {16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31}
    m_batches = calculateBatches(deviceConfig.num_channels, availableCalChannels);
    m_currentBatchIndex = 0;

    // Initialize results storage
    m_allResults.resize(deviceConfig.num_channels);

    emit logMessage(QString("设备 %1 有 %2 个通道, 需要分 %3 批标定")
                        .arg(deviceConfig.name)
                        .arg(deviceConfig.num_channels)
                        .arg(m_batches.size()));

    // 发送初始进度
    emit calibrationProgress(0, 0);

    // 开始第一批次
    setupNextBatch();
}

QVector<QVector<int>> BatchCalibrationManager::calculateBatches(int totalChannels, int maxChannelsPerBatch)
{
    QVector<QVector<int>> batches;

    for (int startCh = 0; startCh < totalChannels; startCh += maxChannelsPerBatch)
    {
        QVector<int> batch;
        int endCh = qMin(startCh + maxChannelsPerBatch, totalChannels);

        for (int ch = startCh; ch < endCh; ++ch)
        {
            batch.append(ch);
        }

        batches.append(batch);
    }

    return batches;
}

void BatchCalibrationManager::setupNextBatch()
{
    if (m_abortRequested)
    {
        emit logMessage("标定过程被中止");
        emit allBatchesCompleted(false);
        return;
    }

    if (m_currentBatchIndex >= m_batches.size())
    {
        // All batches completed
        emit logMessage("所有批次标定完成！");
        emit allBatchesCompleted(true);
        emit saveResultsRequested(m_deviceConfig, m_allResults);
        return;
    }

    auto batchChannels = m_batches[m_currentBatchIndex];
    int batchSize = batchChannels.size(); // 一般为15

    // 获取本批次的第一个通道在全局通道中的索引
    int firstGlobalChannelIndex = batchChannels[0]; // 0\15\30

    // Update the device config for this batch
    CalDeviceConfig batchConfig = m_deviceConfig;
    batchConfig.num_channels = batchSize;
    batchConfig.switchDelay = m_deviceConfig.switchDelay; // 传递开关延迟配置

    // 重置参考值
    batchConfig.ref_values.resize(batchSize);
    for (int i = 0; i < batchSize; ++i)
    {
        // Copy the reference value from the original config based on the real channel index
        batchConfig.ref_values[i] = m_deviceConfig.ref_values[batchChannels[i]];
    }

    // 为本批次计算新的读写首地址
    // 每个通道占用4个寄存器，根据设备类型可能会有所不同
    int registerStride = 0;
    if (m_deviceConfig.name != "618A NTC-32-TIME")
    {
        registerStride = 4; // 默认每通道占用4个寄存器 根据设备型号设为2或4 单精度和双精度
    }
    else
    {
        registerStride = 2;
    }

    // 计算新的首地址
    batchConfig.write_addr = m_deviceConfig.write_addr + (firstGlobalChannelIndex * registerStride);
    batchConfig.read_addr = m_deviceConfig.read_addr + (firstGlobalChannelIndex * registerStride);

    // Create 1220 channel mapping for this batch using the same logic as the original code
    batchConfig.cal_to_1220.clear();
    batchConfig.cal_to_1220.resize(batchSize);

    // 新的1220映射表
    int start_value = 21 - batchSize; // Start from end of available channels

    for (int i = 0; i < batchSize; ++i)
    {
        batchConfig.cal_to_1220[i] = start_value + i;
    }

    // 输出log日志：设备 618A NTC-32 有 32 个通道, 需要分 3 批标定
    emit batchStarting(m_currentBatchIndex + 1, m_batches.size(), batchChannels);

    QVector<int> displayChannels;
    for (int ch : batchChannels)
    {
        displayChannels.append(ch + 1); // Convert to 1-based for display
    }

    // 弹窗提示用户连接通道至 1220 此处应该放到每个批次开始标定流程之前
    emit reconnectionRequired(displayChannels);

    QString channelList;
    for (int i = 0; i < displayChannels.size(); ++i)
    {
        if (i > 0)
            channelList += ", ";
        channelList += QString::number(displayChannels[i]);
    }

    // emit logMessage(QString("批次 %1/%2: 请连接设备通道 %3 到1220通道 %4 - %5")
    //                     .arg(m_currentBatchIndex + 1)
    //                     .arg(m_batches.size())
    //                     .arg(channelList)
    //                     .arg(start_value)
    //                     .arg(start_value + batchSize - 1));

    // 等待用户确认连接 主线程未执行任何操作
    // emit waitingForUserConfirmation(m_currentBatchIndex);

    // Create a new worker for this batch
    m_worker = nullptr;

    m_worker = new CalibrationWorker();

    // Connect signals
    connect(m_worker, &CalibrationWorker::calibrationFinished,
            this, &BatchCalibrationManager::onBatchCalibrationFinished); // 此处保存当前轮次数据
    connect(m_worker, &CalibrationWorker::logMessage,
            this, &BatchCalibrationManager::handleWorkerMessages);
    // connect(m_worker, &CalibrationWorker::calibrationProgress,
    //         this, &BatchCalibrationManager::calibrationProgress);
    connect(m_worker, &CalibrationWorker::calibrationProgress,
            this, [this](int localChannel, int localProgress)
            { calculateAndEmitGlobalProgress(localChannel, localProgress); });
    connect(m_worker, &CalibrationWorker::updateChannelData,
            this, &BatchCalibrationManager::forwardChannelData);
    // connect(m_worker, &CalibrationWorker::saveResultsRequested,
    //         this, &BatchCalibrationManager::handleSaveResultsRequested);

    // Configure worker
    m_worker->setDeviceConfig(batchConfig);
    m_worker->setCommandHandlers(m_calHandler, m_deviceHandler);

    // Start the calibration for this batch
    QThread *thread = new QThread();
    m_worker->moveToThread(thread);
    connect(thread, &QThread::started, m_worker, &CalibrationWorker::startCalibration);
    connect(m_worker, &CalibrationWorker::calibrationFinished, thread, &QThread::quit);
    connect(thread, &QThread::finished, m_worker, &QObject::deleteLater);
    connect(thread, &QThread::finished, thread, &QThread::deleteLater);
    thread->start();
}

void BatchCalibrationManager::calculateAndEmitGlobalProgress(int localChannel, int localProgress)
{
    // 计算总通道数
    int totalChannels = m_deviceConfig.num_channels;
    if (totalChannels <= 0)
        return;

    // 计算已完成的通道数
    int completedChannels = 0;

    // 计算之前批次的已完成通道
    for (int i = 0; i < m_currentBatchIndex; i++)
    {
        completedChannels += m_batches[i].size();
    }

    // 计算当前批次中的进度
    if (m_currentBatchIndex < m_batches.size() && localChannel < m_batches[m_currentBatchIndex].size())
    {
        // 当前通道的进度贡献
        double currentChannelContribution = localProgress / 100.0;

        // 对于已完成的通道，计算为整数
        completedChannels += localChannel;

        // 对于正在处理的通道，加上其部分完成的贡献
        completedChannels += currentChannelContribution;
    }

    // 计算全局进度百分比
    int globalProgress = (completedChannels * 100) / totalChannels;

    // 确保进度范围在0-100
    globalProgress = qMin(100, qMax(0, globalProgress));

    // 获取全局通道号
    int globalChannel = localChannel;
    if (m_currentBatchIndex < m_batches.size() && localChannel < m_batches[m_currentBatchIndex].size())
    {
        globalChannel = m_batches[m_currentBatchIndex][localChannel];
    }

    emit calibrationProgress(globalChannel, globalProgress);
}

void BatchCalibrationManager::onBatchCalibrationFinished(bool success)
{
    if (m_abortRequested)
    {
        emit logMessage("标定过程已中止");
        emit allBatchesCompleted(false);
        return;
    }

    if (!success)
    {
        // // Batch failed, check if we should retry
        // m_batchRetryCount++;

        // if (m_batchRetryCount <= m_maxBatchRetries) {
        //     emit logMessage(QString("批次 %1 标定失败, 正在进行第 %2 次重试...")
        //                         .arg(m_currentBatchIndex + 1)
        //                         .arg(m_batchRetryCount + 1)
        //                         .arg(m_maxBatchRetries));

        //     // Wait a moment before retrying
        //     QThread::sleep(2);

        //     // Retry the same batch
        //     setupNextBatch();
        //     return;
        // } else {
        //     emit logMessage(QString("批次 %1 经过 %2 次尝试仍然失败，终止标定")
        //                         .arg(m_currentBatchIndex + 1)
        //                         .arg(m_maxBatchRetries + 1));

        //     emit allBatchesCompleted(false);
        //     return;
        // }

        emit logMessage(QString("批次 %1 标定失败，终止标定过程")
                            .arg(m_currentBatchIndex + 1));
        emit allBatchesCompleted(false);
        return;
    }

    // Batch successful, collect results
    if (m_worker)
    {
        // 直接从 worker 获取结果
        QVector<QPair<double, double>> batchResults = m_worker->getCalibrationResults();

        // 手动收集结果到全局结果数组
        collectBatchResults(batchResults);

        emit logMessage(QString("批次 %1 标定成功!").arg(m_currentBatchIndex + 1));
        // emit batchCompleted(m_currentBatchIndex, true);

        // 计算总进度
        int completedChannels = 0;
        for (int i = 0; i <= m_currentBatchIndex; i++)
        {
            completedChannels += m_batches[i].size();
        }

        int totalChannels = m_deviceConfig.num_channels;
        int globalProgress = (completedChannels * 100) / totalChannels;

        // 发送批次完成进度
        emit calibrationProgress(m_deviceConfig.num_channels - 1, globalProgress);

        // 确保所有待处理事件已完成
        QCoreApplication::processEvents();

        // 现在才增加批次索引
        m_currentBatchIndex++;

        // 等待片刻后开始下一批次
        QThread::sleep(2);

        // 开始下一批次
        setupNextBatch();
    }
}

void BatchCalibrationManager::handleWorkerMessages(const QString &message)
{
    // // Prefix the message with the current batch information
    // QString prefixedMessage = QString("批次 %1/%2: %3")
    //                               .arg(m_currentBatchIndex + 1)
    //                               .arg(m_batches.size())
    //                               .arg(message);

    // emit logMessage(prefixedMessage);
    // Get the first channel in this batch (0-based)

    int batchStartChannel = 0;
    if (m_currentBatchIndex < m_batches.size() && !m_batches[m_currentBatchIndex].isEmpty())
    {
        batchStartChannel = m_batches[m_currentBatchIndex][0];
    }

    // Create a modified message with corrected channel numbers
    QString modifiedMessage = message;

    // Look for patterns like "通道 X" where X is a number and replace with global channel number
    static QRegExp channelPattern("通道\\s+(\\d+)");
    int pos = 0;
    while ((pos = channelPattern.indexIn(modifiedMessage, pos)) != -1)
    {
        int localChannel = channelPattern.cap(1).toInt();
        int globalChannel = localChannel + batchStartChannel;

        // Replace with global channel number
        modifiedMessage.replace(pos, channelPattern.matchedLength(),
                                QString("通道 %1").arg(globalChannel));

        pos += channelPattern.matchedLength();
    }

    // Prefix the message with the batch information
    QString prefixedMessage = QString("批次 %1/%2: %3")
                                  .arg(m_currentBatchIndex + 1)
                                  .arg(m_batches.size())
                                  .arg(modifiedMessage);

    emit logMessage(prefixedMessage);
}

void BatchCalibrationManager::forwardChannelData(int localChannel, double resistance, double deviation, bool passed)
{
    // Convert local channel index to global channel index
    if (m_currentBatchIndex < m_batches.size())
    {
        auto batchChannels = m_batches[m_currentBatchIndex];
        if (localChannel < batchChannels.size())
        {
            int globalChannel = batchChannels[localChannel];

            // Forward the data with global channel index
            emit updateChannelData(globalChannel, resistance, deviation, passed);
        }
    }
}

// void BatchCalibrationManager::handleSaveResultsRequested(const CalDeviceConfig &config, const QVector<QPair<double, double>> &batchResults)
// {
//     // This is called when a batch is complete and results are available
//     collectBatchResults(batchResults);
// }

void BatchCalibrationManager::collectBatchResults(const QVector<QPair<double, double>> &batchResults)
{
    // Save the results from this batch to our complete results collection
    if (m_currentBatchIndex < m_batches.size())
    {
        auto batchChannels = m_batches[m_currentBatchIndex];

        for (int i = 0; i < batchResults.size() && i < batchChannels.size(); ++i)
        {
            int globalChannel = batchChannels[i];
            if (globalChannel < m_allResults.size())
            {
                m_allResults[globalChannel] = batchResults[i];
            }
        }
    }
}

void BatchCalibrationManager::abortCalibration()
{
    m_abortRequested = true;
    // emit logMessage("正在中止批次标定...");

    if (m_worker)
    {
        m_worker->abortCalibration();
    }
}
