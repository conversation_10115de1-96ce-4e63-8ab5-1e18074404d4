# 进度计算测试

## 测试场景
- 32通道设备
- 3个参考阻值
- 分为3个批次：[15通道, 15通道, 2通道]
- 总工作量：32 × 3 = 96

## 预期进度计算

### 批次1 (15通道)
- 批次工作量：15 × 3 = 45
- 当完成第1个通道第1个阻值时：
  - 批次进度：(0×3 + 1) / 45 × 100 = 2.22% ≈ 2%
  - 全局进度：(0 + 45×2/100) / 96 × 100 = 0.9% ≈ 0%
- 当完成第1个通道第3个阻值时：
  - 批次进度：(0×3 + 3) / 45 × 100 = 6.67% ≈ 6%
  - 全局进度：(0 + 45×6/100) / 96 × 100 = 2.8% ≈ 2%
- 当完成第15个通道第3个阻值时：
  - 批次进度：(14×3 + 3) / 45 × 100 = 100%
  - 全局进度：(0 + 45×100/100) / 96 × 100 = 46.9% ≈ 46%

### 批次2 (15通道)
- 批次工作量：15 × 3 = 45
- 当完成第1个通道第1个阻值时：
  - 批次进度：(0×3 + 1) / 45 × 100 = 2.22% ≈ 2%
  - 全局进度：(45 + 45×2/100) / 96 × 100 = 47.8% ≈ 47%
- 当完成第15个通道第3个阻值时：
  - 批次进度：100%
  - 全局进度：(45 + 45) / 96 × 100 = 93.75% ≈ 93%

### 批次3 (2通道)
- 批次工作量：2 × 3 = 6
- 当完成第1个通道第1个阻值时：
  - 批次进度：(0×3 + 1) / 6 × 100 = 16.67% ≈ 16%
  - 全局进度：(90 + 6×16/100) / 96 × 100 = 95%
- 当完成第2个通道第3个阻值时：
  - 批次进度：100%
  - 全局进度：(90 + 6) / 96 × 100 = 100%

## 修复后的逻辑

### VerificationWorker
```cpp
// 发送批次内进度
int currentBatchStep = ch * m_deviceConfig.ref_index.size() + round + 1;
int totalBatchSteps = m_deviceConfig.num_channels * m_deviceConfig.ref_index.size();
int batchProgress = (currentBatchStep * 100) / totalBatchSteps;
emit calibrationProgress(ch, batchProgress);
```

### BatchVerificationManager
```cpp
// 计算全局进度
int totalWork = m_deviceConfig.num_channels * m_deviceConfig.ref_index.size(); // 96
int completedWork = 0;

// 已完成批次的工作量
for (int i = 0; i < m_currentBatchIndex; i++) {
    completedWork += m_batches[i].size() * m_deviceConfig.ref_index.size();
}

// 当前批次的工作量
if (m_currentBatchIndex < m_batches.size()) {
    int currentBatchWork = m_batches[m_currentBatchIndex].size() * m_deviceConfig.ref_index.size();
    completedWork += (currentBatchWork * localProgress) / 100;
}

int globalProgress = (completedWork * 100) / totalWork;
```

## 关键改进
1. **明确区分批次进度和全局进度**
2. **VerificationWorker只计算批次内进度**
3. **BatchVerificationManager负责全局进度计算**
4. **使用整数运算避免浮点误差**
5. **添加详细的调试日志**
