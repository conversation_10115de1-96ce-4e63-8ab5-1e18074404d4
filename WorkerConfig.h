#ifndef WORKERCONFIG_H
#define WORKERCONFIG_H

#include <QVector>

// 设备标定配置结构体
struct CalDeviceConfig {
    QString name;                // 设备名
    int num_channels;            // 通道个数
    uint16_t write_addr;         // 写入首地址（两字节，例如 0x01A5）
    uint16_t read_addr;          // 读取首地址（两字节，例如 0x0200）
    int ref_index;               // 当前参考电阻索引 1-参考1、2-参考2、3-参考3
    QVector<double> ref_values;  // 每个通道的参考阻值
    QVector<int> cal_to_1220;    // 校准设备通道与1220通道映射
    int switchDelay;             // 开关切换等待时间（秒），默认30秒
};

struct AdjDeviceConfig {
    QString name;                // 设备型号
    int num_channels;            // 通道个数
    QVector<int> ref_index;      // 当前参考电阻通道索引 [1]/[2]/[3] = 1/2/3/4
    QVector<double> ref_values;  // 对应选择的参考阻值 1k、10k、20k
    uint16_t write_addr;         // 写入首地址（两字节，例如 0x01A5）
    uint16_t read_addr;          // 读取首地址（两字节，例如 0x0200）
    QVector<int> cal_to_1220;    // 校准设备通道与1220通道映射 大小与通道个数一致
    int switchDelay;             // 开关切换等待时间（秒），默认30秒
};

// 在头文件中添加枚举定义
enum class FloatPrecision {
    Single,  // 单精度浮点数，4字节，需要2个寄存器
    Double   // 双精度浮点数，8字节，需要4个寄存器
};



#endif // WORKERCONFIG_H
