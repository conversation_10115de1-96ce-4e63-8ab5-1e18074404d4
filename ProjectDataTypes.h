﻿#ifndef PROJECTDATATYPES_H
#define PROJECTDATATYPES_H

#include <QString>
#include <QVector>
#include <QList>
#include <QDateTime>
#include <QJsonObject>
#include <QJsonDocument>

// 通道数据结构
struct ChannelData {
    int channelNumber;
    double referenceValue;              // 输入参考阻值
    QVector<double> measuredResistances;// 四轮阻值
    double measuredResistance;          // 测量阻值 均值
    double deviationFromReference;      // 阻值偏差
    double equivalentTempDeviation;     // 等效温度偏差
    double allowedDeviation;            // 允许偏差
    bool calibrationResult;             // 校准结果
};

// 参考阻值数据结构
struct ReferenceData {
    int referenceId;                    // Channel表的外键
    QString referenceName;              // 参考x
    double referenceValue;              // 输入参考阻值
    QVector<ChannelData> channels;      // 当前参考阻值的所有通道数据
};

struct AmbientInfo {
    QString temp;           // 环境温度
    QString hum;            // 环境湿度
    QString pre;            // 气压
    QString calibrator;     // 校准员
    QString reportNum;      // 报告编号

    QJsonObject toJson() const {
        QJsonObject json;
        json["ambient_temp"] = temp;
        json["ambient_hum"] = hum;
        json["ambient_pre"] = pre;
        json["calibrator"] = calibrator;
        json["report_num"] = reportNum;
        return json;
    }

    static AmbientInfo fromJson(const QJsonObject& obj) {
        AmbientInfo info;
        info.temp = obj["ambient_temp"].toString();
        info.hum = obj["ambient_hum"].toString();
        info.pre = obj["ambient_pre"].toString();
        info.calibrator = obj["calibrator"].toString();
        info.reportNum = obj["report_num"].toString();
        return info;
    }
};

struct DeviceInfo {
    QString type;           // 辅助设备类型（参考电阻、电压源）
    QString model;          // 设备型号（754）
    QString serialNumber;   // 序列号
    QString calibrationDate;// 校准日期

    QJsonObject toJson() const {
        QJsonObject json;
        json["型号"] = model;
            json["序列号"] = serialNumber;
                json["校准日期"] = calibrationDate;
            return json;
    }

    static DeviceInfo fromJson(const QJsonObject& obj, const QString& type) {
        DeviceInfo device;
        device.type = type;
        device.model = obj["型号"].toString();
                           device.serialNumber = obj["序列号"].toString();
                  device.calibrationDate = obj["校准日期"].toString();
              return device;
    }
};

// 项目数据结构
struct ProjectData {
    int projectId;                      // Reference表的外键
    QString projectName;                // 项目名 DAQ23A0043-20250303112233
    QString deviceModel;                // 设备型号 618A
    QString deviceType;                 // 设备类型 精密测温模块
    QString deviceSerialNumber;         // 设备序列号 DAQ23A0043
    QDateTime calibrationDate;          // 校准日期 2025-03-03 11:22:33

    AmbientInfo ambientInfo;
    QList<DeviceInfo> calDeviceInfo;    // 辅助校准设备信息

    QVector<ReferenceData> referenceValues; // 当前项目的所有参考阻值及其对应的通道数据
};

#endif // PROJECTDATATYPES_H
