﻿#ifndef CALIBRATIONDIALOG_H
#define CALIBRATIONDIALOG_H
#include "ui_CalibrationDialog.h"
#include <QDialog>
#include <QButtonGroup>

namespace Ui
{
    class CalibrationDialog;
}

class CalibrationDialog : public QDialog
{
    Q_OBJECT

public:
    explicit CalibrationDialog(QWidget *parent = nullptr);
    ~CalibrationDialog();

    void initCalibrationDialog(const QString &deviceModel, int chNums, const QStringList &refers); // 传入设备型号、通道个数、勾选的参考电阻
    // 公共访问方法
    Ui::CalibrationDialog *getUi() { return ui; }

private:
    Ui::CalibrationDialog *ui;

    QButtonGroup *checkBoxGroup_CalDialog;
    void updateButtonStates(const QString &text);
    void updateUIForBoardCardType(const QString &cardType); // 根据板卡类型更新UI

signals:
    void signal_readCommand_CalDialog(const QString &deviceModel, const QString &featureCode);
    void signal_writeCommand_CalDialog(const QString &deviceModel, const QString &featureCode, const QString &paramVal);
    void signal_writeCommand_CalDialog_Reset(const QString &deviceModel, const QString &featureCode, const QString &paramVal, const QString &referRVal);
    void signal_startCal_CalDialog(const QString &deviceModel, const QString &featureCode, const int channelNums, const QString &referRVal);
    void signal_startVoltageCal_CalDialog(const QString &deviceModel, const QString &featureCode); // 电压校准信号

public slots:
    void on_signal_readCommand_CalDialog_Result(const QString &serialNumber, const QString &featureCode);
    void on_signal_writeCommand_CalDialog_Result(const QString &result, const QString &featureCode);
    void on_signal_startCal_CalDialog_Result(const QString &result, const QString &featureCode);

private slots:
    void on_startCal_CalDialog_clicked();
    void on_startCal_CalDialog_2_clicked(); // 电压校准按钮
    void on_readBtn_CalDialog_clicked();
    void on_writeBtn_CalDialog_clicked();
    void on_init_CalDialog_clicked();
    void on_reset_CalDialog_clicked();
    void on_abortButton_clicked();

    // void onCheckBoxToggled(int id, bool checked);

protected:
    void showEvent(QShowEvent *event) override;
};

#endif // CALIBRATIONDIALOG_H
