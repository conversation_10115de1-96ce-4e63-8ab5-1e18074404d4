#ifndef BATCHVERIFICATION_H
#define BATCHVERIFICATION_H

#include <QObject>
#include <QVector>
#include "VerificationWorker.h"

class BatchVerificationManager : public QObject
{
    Q_OBJECT

public:
    explicit BatchVerificationManager(QObject *parent = nullptr);
    ~BatchVerificationManager();

    // Define command handler function pointer type
    typedef QPair<bool, QString> (*CommandHandlerFunc)(const QByteArray&, const QString&);
    void setCommandHandlers(CommandHandlerFunc calHandler, CommandHandlerFunc deviceHandler);

    // Start batch verification for a device exceeding 1220's channel limit
    void startBatchVerification(const AdjDeviceConfig &deviceConfig,
                                CommandHandlerFunc calHandler,
                                CommandHandlerFunc deviceHandler);

    void abortVerification();

public slots:
    void recalibrateChannel(int groupIndex, double referenceValue, int channelToRecalibrate);
    void onBatchVerificationUiUpdateCompleted();

signals:
    void logMessage(const QString &message);
    void allBatchesCompleted(bool success);
    void batchCompleted(int batchIndex, bool success);
    void calibrationProgress(int channel, int progress);
    void updateChannelData(double referenceValue, int globalChannel, QVector<double> resistances, double measuredResistance,
                           double deviationFromReference, double allowedDeviation, double equivalentTempDeviation, bool calibrationResult);
    void saveResultsRequested();
    void reconnectionRequired(const QVector<int> &channelsToConnect, int referenceIndex);
    void waitingForUserConfirmation(int batchIndex, int referenceIndex);
    void batchStarting(int batchIndex, int totalBatches, int referenceIndex, const QVector<int> &channelsInBatch);
    void uiUpdateFinished();

private slots:
    void onBatchVerificationFinished(bool success);
    void handleWorkerMessages(const QString &message);
    void forwardChannelData(double referenceValue, int localChannel, QVector<double> resistances, double measuredResistance,
                            double deviationFromReference, double allowedDeviation, double equivalentTempDeviation, bool calibrationResult);
    void handleSaveResultsRequested();

private:
    void setupNextBatch();
    QVector<QVector<int>> calculateBatches(int totalChannels, int maxChannelsPerBatch);
    void calculateAndEmitGlobalProgress(int localChannel, int localProgress);
    void cleanupWorker();

    // Recalibration handling
    void onRecalibrationFinished(bool success);
    void handleRecalibrationMessages(const QString &message);

    // Member variables
    AdjDeviceConfig m_deviceConfig;
    CommandHandlerFunc m_calHandler;
    CommandHandlerFunc m_deviceHandler;
    VerificationWorker *m_worker;

    QVector<QVector<int>> m_batches; // Each batch contains channel indices
    int m_currentBatchIndex;
    int m_currentReferenceIndex;
    bool m_abortRequested;

    // Recalibration state
    int m_recalibratingChannel = -1;
    int m_recalibratingGroupIndex = -1;
    double m_recalibratingReferenceValue = 0.0;
};

#endif // BATCHVERIFICATION_H
