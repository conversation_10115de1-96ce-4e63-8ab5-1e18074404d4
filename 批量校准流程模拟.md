# 批量校准流程模拟 (32通道, 4个阻值挡位)

## 初始配置
```
设备: 32通道
参考阻值: 4个 (通道1-4)
参考阻值: [1000Ω, 10000Ω, 20000Ω, 50000Ω]
1220预留通道: 4个 (通道1-4给参考电阻)
可用校准通道: 16个 (通道5-20)
总工作量: 32通道 × 4阻值 = 128步
```

## 批次划分
```
批次1: 通道0-15  (16个通道)
批次2: 通道16-31 (16个通道)
```

## 流程详细模拟

### 阶段1: 初始化
```
BatchVerificationManager::startBatchVerification()
├── m_deviceConfig.num_channels = 32
├── m_deviceConfig.ref_index = [1, 2, 3, 4]
├── m_deviceConfig.ref_values = [1000, 10000, 20000, 50000]
├── referenceReservedChannels = 4
├── availableCalChannels = 16
├── m_batches = [[0,1,2,...,15], [16,17,18,...,31]]
├── m_currentBatchIndex = 0
└── 调用 setupNextBatch()
```

### 阶段2: 批次1开始 (通道0-15)
```
BatchVerificationManager::setupNextBatch()
├── m_currentBatchIndex = 0
├── batchChannels = [0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]
├── batchSize = 16
├── firstGlobalChannelIndex = 0
├── batchConfig.num_channels = 16
├── batchConfig.ref_index = [1,2,3,4] (所有阻值)
├── batchConfig.ref_values = [1000,10000,20000,50000]
├── batchConfig.cal_to_1220 = [5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20]
├── 创建 VerificationWorker
├── worker->setBatchMode(true)
└── 提示用户连接通道1-16到1220的通道5-20
```

### 阶段3: VerificationWorker处理批次1
```
VerificationWorker::startCalibration()
外层循环: 通道 (ch = 0 到 15)
  内层循环: 阻值 (round = 0 到 3)
```

#### 通道0处理
```
ch = 0, calChannel = 5

round = 0 (第1个阻值):
├── referChannel = 1, referValue = 1000
├── 发送1220开关命令: createModbusOpen1220Frame(1, 5)
├── 等待switchDelay秒 (首次连接)
├── 进行4轮测量
├── 计算结果并更新UI
├── currentBatchStep = 0*4 + 0 + 1 = 1
├── emit calibrationProgress(0, 1)
└── BatchVerificationManager收到: completedSteps=1

round = 1 (第2个阻值):
├── referChannel = 2, referValue = 10000
├── 发送1220开关命令: createModbusOpen1220Frame(2, 5)
├── 等待2秒 (切换参考通道)
├── 进行4轮测量
├── 计算结果并更新UI
├── currentBatchStep = 0*4 + 1 + 1 = 2
├── emit calibrationProgress(0, 2)
└── BatchVerificationManager收到: completedSteps=2

round = 2 (第3个阻值):
├── referChannel = 3, referValue = 20000
├── 发送1220开关命令: createModbusOpen1220Frame(3, 5)
├── 等待2秒
├── 进行4轮测量
├── currentBatchStep = 0*4 + 2 + 1 = 3
└── emit calibrationProgress(0, 3)

round = 3 (第4个阻值):
├── referChannel = 4, referValue = 50000
├── 发送1220开关命令: createModbusOpen1220Frame(4, 5)
├── 等待2秒
├── 进行4轮测量
├── currentBatchStep = 0*4 + 3 + 1 = 4
└── emit calibrationProgress(0, 4)
```

#### 通道1处理
```
ch = 1, calChannel = 6

round = 0: currentBatchStep = 1*4 + 0 + 1 = 5
round = 1: currentBatchStep = 1*4 + 1 + 1 = 6
round = 2: currentBatchStep = 1*4 + 2 + 1 = 7
round = 3: currentBatchStep = 1*4 + 3 + 1 = 8
```

#### ... (通道2-14类似)

#### 通道15处理 (批次1最后一个通道)
```
ch = 15, calChannel = 20

round = 0: currentBatchStep = 15*4 + 0 + 1 = 61
round = 1: currentBatchStep = 15*4 + 1 + 1 = 62
round = 2: currentBatchStep = 15*4 + 2 + 1 = 63
round = 3: currentBatchStep = 15*4 + 3 + 1 = 64
```

### 阶段4: 批次1完成
```
VerificationWorker::startCalibration() 结束
├── emit logMessage("所有通道校准完成!")
├── 不发送进度信号 (因为isBatchMode=true)
└── emit calibrationFinished(true)

BatchVerificationManager::onBatchVerificationFinished(true)
├── emit logMessage("批次 1 校准成功! (已完成所有阻值)")
├── m_currentBatchIndex++ (0 → 1)
└── 调用 setupNextBatch()
```

### 阶段5: 批次2开始 (通道16-31)
```
BatchVerificationManager::setupNextBatch()
├── m_currentBatchIndex = 1
├── batchChannels = [16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31]
├── batchSize = 16
├── firstGlobalChannelIndex = 16
├── batchConfig.write_addr = 原地址 + (16 * registerStride)
├── batchConfig.read_addr = 原地址 + (16 * registerStride)
├── batchConfig.cal_to_1220 = [5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20]
└── 提示用户连接通道17-32到1220的通道5-20
```

### 阶段6: VerificationWorker处理批次2
```
类似批次1，但处理的是通道16-31:

通道16: currentBatchStep = 1,2,3,4
通道17: currentBatchStep = 5,6,7,8
...
通道31: currentBatchStep = 61,62,63,64
```

### 阶段7: 全部完成
```
BatchVerificationManager::setupNextBatch()
├── m_currentBatchIndex = 2
├── m_currentBatchIndex >= m_batches.size() (2 >= 2)
├── emit logMessage("所有批次校准完成！")
├── emit allBatchesCompleted(true)
└── emit saveResultsRequested()
```

## 进度计算示例

### BatchVerificationManager::calculateAndEmitGlobalProgress()
```
总工作量 = 32通道 × 4阻值 = 128

批次1进行中:
├── 已完成批次工作量 = 0 (还没有完成的批次)
├── 当前批次工作量 = completedSteps
├── 全局进度 = (0 + completedSteps) * 100 / 128

批次2进行中:
├── 已完成批次工作量 = 16通道 × 4阻值 = 64
├── 当前批次工作量 = completedSteps
├── 全局进度 = (64 + completedSteps) * 100 / 128
```

### 关键进度节点
```
通道1第1个阻值完成: 1/128 = 0.8% ≈ 0%
通道1第4个阻值完成: 4/128 = 3.1% ≈ 3%
通道16第4个阻值完成: 64/128 = 50%
批次2开始通道17第1个阻值: (64+1)/128 = 50.8% ≈ 50%
全部完成: 128/128 = 100%
```

## 1220开关控制命令示例

### 批次1通道0 (设备通道1)
```
阻值1: createModbusOpen1220Frame(1, 5) → 0x11 (0001 0001)
阻值2: createModbusOpen1220Frame(2, 5) → 0x12 (0001 0010)
阻值3: createModbusOpen1220Frame(3, 5) → 0x14 (0001 0100)
阻值4: createModbusOpen1220Frame(4, 5) → 0x18 (0001 1000)
```

### 批次1通道1 (设备通道2)
```
阻值1: createModbusOpen1220Frame(1, 6) → 0x21 (0010 0001)
阻值2: createModbusOpen1220Frame(2, 6) → 0x22 (0010 0010)
阻值3: createModbusOpen1220Frame(3, 6) → 0x24 (0010 0100)
阻值4: createModbusOpen1220Frame(4, 6) → 0x28 (0010 1000)
```

## 关键变量变化总结
```
m_currentBatchIndex: 0 → 1 → 2 (完成)
completedSteps: 1→2→3→4→...→64 (每个批次内重新计数)
globalProgress: 0% → 0% → 1% → 2% → ... → 50% → 50% → 51% → ... → 100%
插拔次数: 2次 (批次1一次，批次2一次)
总校准时间: 大幅减少 (减少了插拔等待时间)
```

## 优化效果对比

### 优化前 (按阻值分组)
```
阻值1: 批次1→批次2 (2次插拔)
阻值2: 批次1→批次2 (2次插拔)
阻值3: 批次1→批次2 (2次插拔)
阻值4: 批次1→批次2 (2次插拔)
总计: 8次插拔
```

### 优化后 (按批次分组)
```
批次1: 阻值1→阻值2→阻值3→阻值4 (1次插拔)
批次2: 阻值1→阻值2→阻值3→阻值4 (1次插拔)
总计: 2次插拔 (减少75%的插拔次数)
```
