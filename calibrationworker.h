﻿#ifndef CALIBRATIONWORKER_H
#define CALIBRATIONWORKER_H

#include <QObject>
#include <QVector>
#include <QPair>
#include <QByteArray>
#include <QDataStream>
#include <QTimer>
#include <QEventLoop>
#include <QThread>
#include <QMutex>
#include <QWaitCondition>
#include <QDebug>
#include <WorkerConfig.h>

class CalibrationWorker : public QObject
{
    Q_OBJECT

public:
    explicit CalibrationWorker(QObject *parent = nullptr);
    ~CalibrationWorker();

    // 设置设备配置
    void setDeviceConfig(const CalDeviceConfig &config);

    // 设置命令发送函数指针
    typedef QPair<bool, QString> (*CommandHandlerFunc)(const QByteArray &, const QString &);
    void setCommandHandlers(CommandHandlerFunc calHandler, CommandHandlerFunc deviceHandler);
    QVector<QPair<double, double>> getCalibrationResults() const;

public slots:
    // 开始校准过程
    void startCalibration();
    // 中止校准过程
    void abortCalibration();

    void restartCalibration(int channel);

signals:
    // 通知UI校准开始
    void calibrationStarted();
    // 通知UI校准完成
    void calibrationFinished(bool success);
    // 更新校准进度
    void calibrationProgress(int channel, int progress);
    // 记录日志消息
    void logMessage(const QString &message);
    // 更新通道数据
    void updateChannelData(int channel, double resistance, double deviation, bool passed);
    // 新增信号，用于传递标定结果到主线程
    void saveResultsRequested(const CalDeviceConfig &config, const QVector<QPair<double, double>> &results);

private:
    double getThresholdValue() const;
    // 辅助函数
    bool is_channel_passed(const QVector<double> &deviations);
    QVector<double> read_resistances(int channel);
    QPair<bool, QPair<double, double>> calibrate_unpassed_channels(int channel, int attempt);

    // Modbus命令生成函数
    QByteArray createModbusReadFrame(uint8_t deviceAddr, uint16_t firstAddr, int channel, int numChannels, FloatPrecision precision = FloatPrecision::Double);
    QByteArray createModbusWriteFrame(uint8_t deviceAddr, uint16_t firstAddr, int channel, double ref_value, FloatPrecision precision = FloatPrecision::Double);
    QByteArray createModbusOpen1220Frame(int referChannel, int calChannel);
    QByteArray createModbusCommand(const QString &hexCommand);

    // 发送命令
    QPair<bool, QString> sendCommand(const QByteArray &command, const QString &commandType);

    // 计算CRC
    uint16_t calculateCRC16(const QByteArray &data);

    // 成员变量
    CalDeviceConfig m_deviceConfig;
    bool m_abortRequested;

    // 命令处理函数指针
    CommandHandlerFunc m_calCommandHandler;
    CommandHandlerFunc m_deviceCommandHandler;

    // void saveCalibrationResults(const CalDeviceConfig &config, const QVector<QPair<double, double>> &results);
    QVector<QPair<double, double>> m_finalResults; // 存储最终结果

    // 根据设备型号确定浮点精度
    FloatPrecision getDevicePrecision() const;

    // 可中断的等待方法
    bool interruptibleSleep(int seconds);
};

#endif // CALIBRATIONWORKER_H
