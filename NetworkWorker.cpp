﻿// NetworkWorker.cpp
#include "NetworkWorker.h"
#include "DataProcessor.h"

NetworkWorker::NetworkWorker(QObject *parent) : QObject(parent),
                                                m_isTimeout(false),
                                                m_isWaitingResponse(false)
{
    m_socket = new QTcpSocket(this);
    m_receiveTimer = new QTimer(this);
    m_timeoutTimer = new QTimer(this);

    m_receiveTimer->setSingleShot(true);
    m_timeoutTimer->setSingleShot(true);

    connect(m_socket, &QTcpSocket::readyRead, this, &NetworkWorker::handleReadyRead);
    connect(m_socket, &QTcpSocket::errorOccurred, this, &NetworkWorker::handleSocketError);
    connect(m_receiveTimer, &QTimer::timeout, this, &NetworkWorker::processReceivedData);
    connect(m_timeoutTimer, &QTimer::timeout, this, &NetworkWorker::handleTimeout);
}

NetworkWorker::~NetworkWorker()
{
    disconnectFromHost();

    m_processors.clear();
    // qDeleteAll(m_processors);
}

void NetworkWorker::connectToHost(const QString &host, quint16 port)
{
    QMutexLocker locker(&m_mutex);

    if (m_socket->state() == QAbstractSocket::ConnectedState)
    {
        m_socket->disconnectFromHost();
    }

    m_socket->connectToHost(host, port);

    if (m_socket->waitForConnected(5000))
    {
        emit operationCompleted(true, "网络连接成功", "connect");
    }
    else
    {
        emit operationCompleted(false, "连接失败: " + m_socket->errorString(), "connect");
    }
}

void NetworkWorker::disconnectFromHost()
{
    QMutexLocker locker(&m_mutex);

    if (m_socket->state() == QAbstractSocket::ConnectedState)
    {
        m_socket->disconnectFromHost();
    }
    emit operationCompleted(true, "网络已断开", "disconnect");
}

// void NetworkWorker::writeData(const QByteArray &data, const QString &command)
//{
//     QMutexLocker locker(&m_mutex);

//    if(m_socket->state() != QAbstractSocket::ConnectedState) {
//        emit operationCompleted(false, "网络未连接", command);
//        return;
//    }

//    clearBuffers();
//    m_currentCommand = command;
//    m_isTimeout = false;

//    if(m_socket->write(data) == data.size()) {
//        m_socket->flush();
//        m_timeoutTimer->start(5000); // 5秒超时
//    } else {
//        emit operationCompleted(false, "数据发送失败", command);
//    }
//}

void NetworkWorker::writeData(const QByteArray &data, const QString &command)
{
    QMutexLocker locker(&m_mutex);

    if (m_socket->state() != QAbstractSocket::ConnectedState)
    {
        emit operationCompleted(false, "网络未连接", command);
        return;
    }

    clearBuffers();
    stopTimers(); // 停止所有定时器

    m_currentCommand = command;
    m_isTimeout = false;
    m_isWaitingResponse = true; // 标记正在等待响应

    if (m_socket->write(data) == data.size())
    {
        m_socket->flush();
        m_timeoutTimer->start(5000); // 5秒超时
    }
    else
    {
        m_isWaitingResponse = false;
        emit operationCompleted(false, "数据发送失败", command);
    }
}

// void NetworkWorker::handleReadyRead()
//{
//     if(!m_receiveTimer->isActive()) {
//         m_receiveTimer->start(100); // 100ms 接收延时
//     }
//     m_receivedData.append(m_socket->readAll());
// }

void NetworkWorker::handleReadyRead()
{
    if (!m_isWaitingResponse || m_isTimeout)
    {
        // 不在等待响应状态或已超时，忽略数据
        m_socket->readAll(); // 清空缓冲区但不处理
        return;
    }

    if (!m_receiveTimer->isActive())
    {
        m_receiveTimer->start(100); // 100ms 接收延时
    }
    m_receivedData.append(m_socket->readAll());
}

// void NetworkWorker::processReceivedData()
//{
//     QMutexLocker locker(&m_mutex);

//    if(m_isTimeout || m_currentCommand.isEmpty()) {
//        return;
//    }

//    for(DataProcessor* processor : m_processors) {
//        if(processor->supportsCommand(m_currentCommand)) {
//            processor->processData(m_receivedData, m_currentCommand);
//            clearBuffers();
//            return;
//        }
//    }
//}
void NetworkWorker::processReceivedData()
{
    QMutexLocker locker(&m_mutex);

    if (m_isTimeout || !m_isWaitingResponse || m_currentCommand.isEmpty())
    {
        return;
    }

    bool processed = false;
    for (DataProcessor *processor : m_processors)
    {
        if (processor->supportsCommand(m_currentCommand))
        {
            processor->processData(m_receivedData, m_currentCommand);
            processed = true;
            break;
        }
    }

    // 如果没有处理器能处理此命令，也应该完成操作
    if (!processed)
    {
        emit operationCompleted(false, "找不到匹配的处理器", m_currentCommand);
    }

    m_isWaitingResponse = false;
    stopTimers();
    clearBuffers();
}

// void NetworkWorker::handleTimeout()
//{
//     QMutexLocker locker(&m_mutex);
//     m_isTimeout = true;
//     emit operationCompleted(false, "操作超时", m_currentCommand);
//     clearBuffers();
// }
void NetworkWorker::handleTimeout()
{
    QMutexLocker locker(&m_mutex);

    if (!m_isWaitingResponse)
    {
        return; // 如果不是在等待响应状态，忽略超时
    }

    m_isTimeout = true;
    m_isWaitingResponse = false;
    stopTimers(); // 停止所有计时器

    emit operationCompleted(false, "操作超时", m_currentCommand);
    clearBuffers();
}

// void NetworkWorker::handleSocketError(QAbstractSocket::SocketError error)
//{
//     emit operationCompleted(false, "网络错误: " + m_socket->errorString(), m_currentCommand);
// }
void NetworkWorker::handleSocketError(QAbstractSocket::SocketError error)
{
    QMutexLocker locker(&m_mutex);

    if (!m_isWaitingResponse)
    {
        return; // 如果不是在等待响应状态，忽略错误
    }

    m_isWaitingResponse = false;
    stopTimers();

    emit operationCompleted(false, "网络错误: " + m_socket->errorString(), m_currentCommand);
    clearBuffers();
}

void NetworkWorker::clearBuffers()
{
    m_receivedData.clear();
    m_currentCommand.clear();
    m_socket->flush();
}

void NetworkWorker::stopTimers()
{
    if (m_receiveTimer->isActive())
    {
        m_receiveTimer->stop();
    }
    if (m_timeoutTimer->isActive())
    {
        m_timeoutTimer->stop();
    }
}

void NetworkWorker::registerProcessor(DataProcessor *processor)
{
    QMutexLocker locker(&m_mutex);
    if (!m_processors.contains(processor))
    {
        m_processors.append(processor);
        connect(processor, &DataProcessor::resultReady,
                this, [this](bool success, const QString &result, const QString &command)
                { emit operationCompleted(success, result, command); });
    }
}

bool NetworkWorker::isConnected() const
{
    return m_socket->state() == QAbstractSocket::ConnectedState;
}
