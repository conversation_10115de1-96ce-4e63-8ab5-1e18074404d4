name: Build AutoCalib

on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]
  workflow_dispatch:

jobs:
  build:
    runs-on: windows-latest
    
    steps:
    - uses: actions/checkout@v4
      with:
        submodules: recursive
        
    - name: Cache Qt
      id: cache-qt
      uses: actions/cache@v4
      with:
        path: ${{ runner.workspace }}/Qt
        key: ${{ runner.os }}-qt-${{ hashFiles('**/CMakeLists.txt') }}
        restore-keys: |
          ${{ runner.os }}-qt-
        
    - name: Install Qt
      uses: jurplel/install-qt-action@v3
      with:
        version: '5.15.2'
        host: 'windows'
        target: 'desktop'
        arch: 'win64_msvc2019_64'
        dir: ${{ runner.workspace }}/Qt
        install-deps: 'true'
        cache: ${{ steps.cache-qt.outputs.cache-hit }}
        
    - name: Setup Visual Studio Environment
      uses: ilammy/msvc-dev-cmd@v1
      with:
        arch: x64
        
    - name: Configure and Build
      run: |
        mkdir build
        cd build
        
        # 检查项目中是否有CMakeLists.txt
        if (Test-Path -Path "../CMakeLists.txt") {
          # 使用CMake构建
          cmake .. -G "Visual Studio 17 2022" -A x64 -DCMAKE_PREFIX_PATH=${{ env.Qt5_DIR }}
          cmake --build . --config Release
        } else {
          # 假设使用qmake构建
          qmake ../AutoCalib.pro CONFIG+=release
          nmake
        }
        
    - name: Deploy Qt Dependencies
      working-directory: build
      run: |
        if (Test-Path -Path "Release") {
          cd Release
        }
        # 假设主可执行文件名为AutoCalib.exe
        if (Test-Path -Path "AutoCalib.exe") {
          windeployqt.exe --release AutoCalib.exe
        } else {
          # 尝试查找.exe文件
          $exeFile = Get-ChildItem -Path . -Filter "*.exe" | Select-Object -First 1
          if ($exeFile) {
            windeployqt.exe --release $exeFile.Name
          }
        }
        
    - name: Package Application
      run: |
        mkdir package
        if (Test-Path -Path "build/Release") {
          Copy-Item -Path "build/Release/*" -Destination "package/" -Recurse
        } else {
          Copy-Item -Path "build/*.exe" -Destination "package/" -ErrorAction SilentlyContinue
          Copy-Item -Path "build/*.dll" -Destination "package/" -ErrorAction SilentlyContinue
        }
        
    - name: Upload Artifacts
      uses: actions/upload-artifact@v4  # 已将v3更新为v4
      with:
        name: AutoCalib-Windows
        path: package/
