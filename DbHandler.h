﻿#pragma once

#include <QObject>
#include <QSqlDatabase>
#include <QSqlQuery>
#include <QSqlError>
#include <QSqlTableModel>
#include <QSqlRecord>
#include <QMutex>
#include <QThread>
#include <QDebug>
#include <QVariant>

typedef QMap<QString, QVariant> RecordData;

class DatabaseWorker : public QObject
{
    Q_OBJECT

public:
    explicit DatabaseWorker(QObject *parent = nullptr);
    ~DatabaseWorker();

    bool isDbOpen() const;

public slots:
    void connectToDatabase(const QString &databaseName);
    void closeDatabase();
    bool insertRecord(const QString &tableName, const RecordData &data);
    bool updateRecord(const QString &tableName, const QString &condition, const RecordData &data);
    bool deleteRecord(const QString &tableName, const QString &condition);
    bool clearAllRecord(const QString &tableName, const QString &condition);
    bool queryRecords(const QString &tableName, const QString &condition);
    bool queryRecordsPro(const QString &sqlQuery);
    void getModel(const QString &tableName);

    //事务
    void beginTransaction();
    void commitTransaction();
    void rollbackTransaction();

signals:
    void operationResult(bool success, const QString &message);
    void recordsQueried(bool success, const QList<QSqlRecord> &records);
    void modelReady(QSqlTableModel* model);

private:
    QSqlDatabase db;

    //QMutex m_workerMutex;
    //bool m_isConnected;
};

class DbHandler : public QObject
{
    Q_OBJECT

public:
    explicit DbHandler(QObject *parent = nullptr);
    ~DbHandler();

    // typedef QMap<QString, QVariant> RecordData;

    bool connectToDatabase(const QString &databaseName);
    void closeDatabase();
    bool insertRecord(const QString &tableName, const RecordData &data);
    bool updateRecord(const QString &tableName, const QString &condition, const RecordData &data);
    bool deleteRecord(const QString &tableName, const QString &condition);
    bool clearAllRecord(const QString &tableName, const QString &condition = "1=1");

    QPair<bool, QList<QSqlRecord>> queryRecords(const QString &tableName, const QString &condition = "");
    QPair<bool, QList<QSqlRecord>> queryRecordsPro(const QString &sqlQuery);
    QPair<bool, QSqlTableModel*> getModel(const QString &tableName);

    QVariant getScalarValue(const QString &sqlQuery);
    QList<QVariant> getColumnValues(const QString &sqlQuery);

    //bool queryRecords(const QString &tableName, const QString &condition = "");
    //bool queryRecordsPro(const QString &sqlQuery);
    //QSqlTableModel* getModel(const QString &tableName);

    bool beginTransaction();
    bool commitTransaction();
    bool rollbackTransaction();
    bool isDatabaseConnected() const;

signals:
    void connectToDatabaseSignal(const QString &databaseName);
    void closeDatabaseSignal();
    void insertRecordSignal(const QString &tableName, const RecordData &data);
    void updateRecordSignal(const QString &tableName, const QString &condition, const RecordData &data);
    void deleteRecordSignal(const QString &tableName, const QString &condition);
    void clearAllRecordSignal(const QString &tableName, const QString &condition);
    void queryRecordsSignal(const QString &tableName, const QString &condition);
    void queryRecordsProSignal(const QString &sqlQuery);
    void getModelSignal(const QString &tableName);

    void operationResult(bool success, const QString &message);
    void recordsQueried(bool success, const QList<QSqlRecord> &records);

    void modelReady(QSqlTableModel* model);

    void beginTransactionSignal();
    void commitTransactionSignal();
    void rollbackTransactionSignal();

private slots:
    void onOperationResult(bool success, const QString &message);
    void onRecordsQueried(bool success, const QList<QSqlRecord> &records);
    void onModelReady(QSqlTableModel* model);

private:
    DatabaseWorker *m_worker;
    QThread *m_thread;
    QMutex m_mutex;
    bool m_operationResult;
    QList<QSqlRecord> m_queryResult;
    QSqlTableModel* m_model;
};
