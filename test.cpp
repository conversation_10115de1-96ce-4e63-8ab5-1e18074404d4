#include "ExportTask.h"
#include "qtimer.h"
#include <QDir>
#include <QCoreApplication>

ExportTask::ExportTask(const QVariantMap& data) : data(data) {}


void ExportTask::process() {

    CoInitialize(NULL);

    // 新建一个word应用程序
    QAxObject* word = new QAxObject("Word.Application");
    // 并设置为不可见
    word->setProperty("Visible", false);
    // 获取所有的工作文档
    QAxObject *documents = word->querySubObject("Documents");
    // 以模板新建一个文档
    documents->dynamicCall("Add(QString)", data["templatePath"].toString());
    // 获取当前激活的文档
    QAxObject *document = word->querySubObject("ActiveDocument");

    bool is1611A_HT_PT100 = data["is1611A_HT_PT100"].toBool();
    bool is1611A_HT_PT1000 = data["is1611A_HT_PT1000"].toBool();

    // 使用传递过来的数据替换书签
    replaceBookmarkText(document, "lineEdit1", data["lineEdit1"].toString());
    replaceBookmarkText(document, "reportNum", data["reportNum"].toString());
    replaceBookmarkText(document, "ambientTemp", data["ambientTemp"].toString());
    replaceBookmarkText(document, "ambientHum", data["ambientHum"].toString());
    if (is1611A_HT_PT100) {
        replaceBookmarkText(document, "deviceModel", "1611A-HT");
    } else if (is1611A_HT_PT1000) {
        replaceBookmarkText(document, "deviceModel", "1611A-HT");
    } else {
        replaceBookmarkText(document, "deviceModel", data["deviceModel"].toString());
    }
    //replaceBookmarkText(document, "deviceModel", data["deviceModel"].toString());
    replaceBookmarkText(document, "ambientPre", data["ambientPre"].toString());
    replaceBookmarkText(document, "calDate", data["calDate"].toString());
    replaceBookmarkText(document, "calMethod1", data["calMethod1"].toString());
    replaceBookmarkText(document, "calibrator", data["calibrator"].toString());
    replaceBookmarkText(document, "calDate2", data["calDate"].toString());

    replaceBookmarkText(document, "deviceName", data["deviceName"].toString());

    // 校准设备新增行
    QAxObject* table = document->querySubObject("Tables(int)", 2); // 获取文档中的第二个表
    QAxObject* rows = table->querySubObject("Rows");

    foreach (const QVariant& row, data["tableRows"].toList()) {
        addRowToTable(rows, row.toMap());
    }

    // 清理
    delete rows;
    delete table;

    // 获取 Word 文档的书签位置
    QAxObject* bookmark = document->querySubObject("Bookmarks(QString)", "testData");
    QAxObject* range = bookmark->querySubObject("Range()");

    bool isTM18MD_P = data["isTM18MD_P"].toBool();
    bool isTM18 = data["isTM18"].toBool();
    bool isTM18ND = data["isTM18ND"].toBool();
    bool isTM18RD_P = data["isTM18RD_P"].toBool();
    bool is618A_TC = data["is618A_TC"].toBool();
    bool isTM228ND = data["isTM228ND"].toBool();
    bool is1611A_NTC = data["is1611A_NTC"].toBool();
    bool is1611A_NTC_Plus = data["is1611A_NTC_Plus"].toBool();
    bool is1611A_RTD = data["is1611A_RTD"].toBool();
    bool is1611A_RTD_Plus = data["is1611A_RTD_Plus"].toBool();
    bool is1611A_TC = data["is1611A_TC"].toBool();
    bool is1611A_VS = data["is1611A_VS"].toBool();
    bool is618A_TC_12 = data["is618A_TC_12"].toBool();

    if (isTM18MD_P) {
        QStringList headers_1;

        headers_1 = QStringList({
            "通道",
            "参考阻值(" + data["refResUnit"].toString() + ")",
            "测量阻值(" + data["measResUnit"].toString() + ")",
            "阻值偏差(" + data["devUnit"].toString() + ")",
            "允许偏差(Ω)",
            "校准结果"
        });

        int numRows_1 = data["measurements_2"].toList().size();
        QAxObject* tables_1 = document->querySubObject("Tables");

        int columnCount_1 = 6;

        QAxObject* newTable_1 = tables_1->querySubObject("Add(QVariant, int, int)", range->asVariant(), numRows_1 + 1, columnCount_1);

        for (int i = 0; i < headers_1.size(); ++i) {
            QAxObject* cell = newTable_1->querySubObject("Cell(Row, Column)", 1, i + 1);
            QAxObject* range = cell->querySubObject("Range");
            range->setProperty("Text", headers_1[i]);

            QAxObject* font = range->querySubObject("Font");
            font->setProperty("Name", "宋体");
            font->setProperty("Size", 9);
            font->setProperty("Bold", true);

            QAxObject* borders = cell->querySubObject("Borders");
            for (int j = 1; j <= 4; ++j) {  // 1-4 表示上下左右边框
                QAxObject* border = borders->querySubObject("Item(int)", j);
                border->setProperty("LineStyle", 1);  // 1表示单实线
            }

            // 设置单元格内容居中
            QAxObject* paragraphFormat = range->querySubObject("ParagraphFormat");
            paragraphFormat->setProperty("Alignment", 1);  // 1表示居中对齐
        }

        int rowIndex = 2;

        for (const QVariant &measurementVariant : data["measurements_2"].toList()) {
            QVariantMap measurement = measurementVariant.toMap();

            for (int col = 1; col <= columnCount_1; ++col) {
                QAxObject* cell = newTable_1->querySubObject("Cell(Row, Column)", rowIndex, col);
                QAxObject* range = cell->querySubObject("Range");

                double refResValue = measurement["refRes"].toString().toDouble();
                double allowedDev = calculateAllowedDev("TM18MD-P-T1", refResValue); //与TM18RD-P一致 V1.1.18

                switch (col) {
                case 1: range->setProperty("Text", measurement["channel"].toString()); break;
                case 2: range->setProperty("Text", measurement["refRes"].toString()); break;
                case 3: range->setProperty("Text", measurement["measRes"].toString()); break;
                case 4: range->setProperty("Text", measurement["devRes"].toString()); break;
                case 5: range->setProperty("Text", QString::number(allowedDev, 'f', 2)); break;
                case 6:
                    double devValue = qAbs(measurement["devRes"].toString().toDouble());
                    range->setProperty("Text", devValue <= allowedDev ? "P" : "F"); //
                    break;
                }
                // 设置单元格样式
                setCellStyle(cell);
            }
            rowIndex++;
        }

        // 设置除表头外所有单元格的边框和字体
        for (int row = 2; row <= rowIndex; ++row) {
            for (int col = 1; col <= columnCount_1; ++col) {
                QAxObject* cell = newTable_1->querySubObject("Cell(Row, Column)", row, col);
                QAxObject* range = cell->querySubObject("Range");

                QAxObject* font = range->querySubObject("Font");
                font->setProperty("Name", "宋体");
                font->setProperty("Size", 9);
                font->setProperty("Bold", false);

                QAxObject* paragraphFormat = range->querySubObject("ParagraphFormat");
                paragraphFormat->setProperty("Alignment", 1);  // 1表示居中对齐

                QAxObject* borders = cell->querySubObject("Borders");
                for (int i = 1; i <= 4; ++i) {  // 1-4 表示上下左右边框
                    QAxObject* border = borders->querySubObject("Item(int)", i);
                    border->setProperty("LineStyle", 1);  // 1表示单实线
                }
            }
        }

        // 合并相同参考阻值的单元格
        mergeSameRefResCells(newTable_1, data["measurements_2"].toList());


        // 移动光标到第一个表格的末尾
        range = newTable_1->querySubObject("Range()");
        range->dynamicCall("Collapse(int)", 0); // 0表示光标移动到范围的末尾

        // 插入一个空行
        range->dynamicCall("InsertParagraphAfter()");

        // 移动光标到新插入的段落末尾
        range = range->querySubObject("Paragraphs(1)")->querySubObject("Range()");
        range->dynamicCall("Collapse(int)", 0); // 0表示光标移动到范围的末尾


        // 表2
        QStringList headers_2;

        headers_2 = QStringList({
            "通道",
            "参考阻值(" + data["refResUnit"].toString() + ")",
            "测量阻值(" + data["measResUnit"].toString() + ")",
            "阻值偏差(" + data["devUnit"].toString() + ")",
            "允许偏差(Ω)",
            "校准结果"
        });

        int numRows_2 = data["measurements"].toList().size();
        QAxObject* tables_2 = document->querySubObject("Tables");

        int columnCount_2 = 6;

        QAxObject* newTable_2 = tables_2->querySubObject("Add(QVariant, int, int)", range->asVariant(), numRows_2 + 1, columnCount_2);

        for (int i = 0; i < headers_2.size(); ++i) {
            QAxObject* cell = newTable_2->querySubObject("Cell(Row, Column)", 1, i + 1);
            QAxObject* range = cell->querySubObject("Range");
            range->setProperty("Text", headers_2[i]);

            QAxObject* font = range->querySubObject("Font");
            font->setProperty("Name", "宋体");
            font->setProperty("Size", 9);
            font->setProperty("Bold", true);

            QAxObject* borders = cell->querySubObject("Borders");
            for (int j = 1; j <= 4; ++j) {  // 1-4 表示上下左右边框
                QAxObject* border = borders->querySubObject("Item(int)", j);
                border->setProperty("LineStyle", 1);  // 1表示单实线
            }

            // 设置单元格内容居中
            QAxObject* paragraphFormat = range->querySubObject("ParagraphFormat");
            paragraphFormat->setProperty("Alignment", 1);  // 1表示居中对齐
        }

        int rowIndex_2 = 2;

        // 该部分代码用来替换表格从T1开始->T2开始
        // 假设 data 是包含 measurements 的 QVariantMap
        QVariantList measurements = data["measurements"].toList();

        // 创建一个临时存储新的 channel 值的列表
        QList<QString> newChannels;

        // 遍历 measurements，构建新的 channel 列表
        for (const QVariant &measurementVariant : measurements) {
            QVariantMap measurement = measurementVariant.toMap();
            QString currentChannel = measurement["channel"].toString();

            // 提取数字部分并增加
            bool ok;
            int channelNumber = currentChannel.mid(1).toInt(&ok); // 提取 T 后面的数字
            if (ok) {
                newChannels.append(QString("T%1").arg(channelNumber + 1)); // 增加 1
            } else {
                newChannels.append(currentChannel); // 如果解析失败，保持原值
            }
        }

        // 在原始数据中替换 channel 值
        for (int i = 0; i < measurements.size(); ++i) {
            QVariantMap measurement = measurements[i].toMap();
            measurement["channel"] = newChannels[i]; // 更新 channel 为新的值
            measurements[i] = measurement; // 重新赋值回原列表
        }


        for (const QVariant &measurementVariant : measurements) {
            QVariantMap measurement = measurementVariant.toMap();

            for (int col = 1; col <= columnCount_2; ++col) {
                QAxObject* cell = newTable_2->querySubObject("Cell(Row, Column)", rowIndex_2, col);
                QAxObject* range = cell->querySubObject("Range");

                double refResValue = measurement["refRes"].toString().toDouble();
                double allowedDev = calculateAllowedDev("TM18MD-P-T2-8", refResValue); //与TM18RD-P一致 V1.1.18
                switch (col) {
                case 1: range->setProperty("Text", measurement["channel"].toString()); break;
                case 2: range->setProperty("Text", measurement["refRes"].toString()); break;
                case 3: range->setProperty("Text", measurement["measRes"].toString()); break;
                case 4: range->setProperty("Text", measurement["devRes"].toString()); break;
                case 5: range->setProperty("Text", QString::number(allowedDev, 'f', 2)); break;
                case 6:
                    double devValue = qAbs(measurement["devRes"].toString().toDouble());
                    range->setProperty("Text", devValue <= allowedDev ? "P" : "F"); //电压校准结果
                    break;
                }
                // 设置单元格样式
                setCellStyle(cell);
            }
            rowIndex_2++;
        }

        // 设置除表头外所有单元格的边框和字体
        for (int row = 2; row <= rowIndex_2; ++row) {
            for (int col = 1; col <= columnCount_2; ++col) {
                QAxObject* cell = newTable_2->querySubObject("Cell(Row, Column)", row, col);
                QAxObject* range = cell->querySubObject("Range");

                QAxObject* font = range->querySubObject("Font");
                font->setProperty("Name", "宋体");
                font->setProperty("Size", 9);
                font->setProperty("Bold", false);

                QAxObject* paragraphFormat = range->querySubObject("ParagraphFormat");
                paragraphFormat->setProperty("Alignment", 1);  // 1表示居中对齐

                QAxObject* borders = cell->querySubObject("Borders");
                for (int i = 1; i <= 4; ++i) {  // 1-4 表示上下左右边框
                    QAxObject* border = borders->querySubObject("Item(int)", i);
                    border->setProperty("LineStyle", 1);  // 1表示单实线
                }
            }
        }

        // 合并相同参考阻值的单元格
        mergeSameRefResCells(newTable_2, data["measurements"].toList());
    }
    else if (isTM18) {
        QStringList headers_1;

        headers_1 = QStringList({
            "通道",
            "参考阻值(" + data["refResUnit"].toString() + ")",
            "测量阻值(" + data["measResUnit"].toString() + ")",
            "阻值偏差(" + data["devUnit"].toString() + ")",
            "允许偏差(Ω)",
            "校准结果"
        });

        int numRows_1 = data["measurements_2"].toList().size();
        QAxObject* tables_1 = document->querySubObject("Tables");

        int columnCount_1 = 6;

        QAxObject* newTable_1 = tables_1->querySubObject("Add(QVariant, int, int)", range->asVariant(), numRows_1 + 1, columnCount_1);

        for (int i = 0; i < headers_1.size(); ++i) {
            QAxObject* cell = newTable_1->querySubObject("Cell(Row, Column)", 1, i + 1);
            QAxObject* range = cell->querySubObject("Range");
            range->setProperty("Text", headers_1[i]);

            QAxObject* font = range->querySubObject("Font");
            font->setProperty("Name", "宋体");
            font->setProperty("Size", 9);
            font->setProperty("Bold", true);

            QAxObject* borders = cell->querySubObject("Borders");
            for (int j = 1; j <= 4; ++j) {  // 1-4 表示上下左右边框
                QAxObject* border = borders->querySubObject("Item(int)", j);
                border->setProperty("LineStyle", 1);  // 1表示单实线
            }

            // 设置单元格内容居中
            QAxObject* paragraphFormat = range->querySubObject("ParagraphFormat");
            paragraphFormat->setProperty("Alignment", 1);  // 1表示居中对齐
        }

        int rowIndex = 2;

        for (const QVariant &measurementVariant : data["measurements_2"].toList()) {
            QVariantMap measurement = measurementVariant.toMap();

            for (int col = 1; col <= columnCount_1; ++col) {
                QAxObject* cell = newTable_1->querySubObject("Cell(Row, Column)", rowIndex, col);
                QAxObject* range = cell->querySubObject("Range");

                switch (col) {
                case 1: range->setProperty("Text", measurement["channel"].toString()); break;
                case 2: range->setProperty("Text", measurement["refRes"].toString()); break;
                case 3: range->setProperty("Text", measurement["measRes"].toString()); break;
                case 4: range->setProperty("Text", measurement["devRes"].toString()); break;
                case 5: range->setProperty("Text", QString::number(0.1, 'f', 1)); break;
                case 6:
                    double devValue = qAbs(measurement["devRes"].toString().toDouble());
                    range->setProperty("Text", devValue <= 0.1 ? "P" : "F"); //
                    break;
                }
                // 设置单元格样式
                setCellStyle(cell);
            }
            rowIndex++;
        }

        // 设置除表头外所有单元格的边框和字体
        for (int row = 2; row <= rowIndex; ++row) {
            for (int col = 1; col <= columnCount_1; ++col) {
                QAxObject* cell = newTable_1->querySubObject("Cell(Row, Column)", row, col);
                QAxObject* range = cell->querySubObject("Range");

                QAxObject* font = range->querySubObject("Font");
                font->setProperty("Name", "宋体");
                font->setProperty("Size", 9);
                font->setProperty("Bold", false);

                QAxObject* paragraphFormat = range->querySubObject("ParagraphFormat");
                paragraphFormat->setProperty("Alignment", 1);  // 1表示居中对齐

                QAxObject* borders = cell->querySubObject("Borders");
                for (int i = 1; i <= 4; ++i) {  // 1-4 表示上下左右边框
                    QAxObject* border = borders->querySubObject("Item(int)", i);
                    border->setProperty("LineStyle", 1);  // 1表示单实线
                }
            }
        }

        // 合并相同参考阻值的单元格
        mergeSameRefResCells(newTable_1, data["measurements_2"].toList());


        // 移动光标到第一个表格的末尾
        range = newTable_1->querySubObject("Range()");
        range->dynamicCall("Collapse(int)", 0); // 0表示光标移动到范围的末尾

        // 插入一个空行
        range->dynamicCall("InsertParagraphAfter()");

        // 移动光标到新插入的段落末尾
        range = range->querySubObject("Paragraphs(1)")->querySubObject("Range()");
        range->dynamicCall("Collapse(int)", 0); // 0表示光标移动到范围的末尾


        // 表2
        QStringList headers_2;

        headers_2 = QStringList({
            "通道",
            "参考阻值(" + data["refResUnit"].toString() + ")",
            "测量阻值(" + data["measResUnit"].toString() + ")",
            "阻值偏差(" + data["devUnit"].toString() + ")",
            "允许偏差(Ω)",
            "校准结果"
        });

        int numRows_2 = data["measurements"].toList().size();
        QAxObject* tables_2 = document->querySubObject("Tables");

        int columnCount_2 = 6;

        QAxObject* newTable_2 = tables_2->querySubObject("Add(QVariant, int, int)", range->asVariant(), numRows_2 + 1, columnCount_2);

        for (int i = 0; i < headers_2.size(); ++i) {
            QAxObject* cell = newTable_2->querySubObject("Cell(Row, Column)", 1, i + 1);
            QAxObject* range = cell->querySubObject("Range");
            range->setProperty("Text", headers_2[i]);

            QAxObject* font = range->querySubObject("Font");
            font->setProperty("Name", "宋体");
            font->setProperty("Size", 9);
            font->setProperty("Bold", true);

            QAxObject* borders = cell->querySubObject("Borders");
            for (int j = 1; j <= 4; ++j) {  // 1-4 表示上下左右边框
                QAxObject* border = borders->querySubObject("Item(int)", j);
                border->setProperty("LineStyle", 1);  // 1表示单实线
            }

            // 设置单元格内容居中
            QAxObject* paragraphFormat = range->querySubObject("ParagraphFormat");
            paragraphFormat->setProperty("Alignment", 1);  // 1表示居中对齐
        }

        // 获取表1的通道数
        int M;
        // 获取 measurements_2 列表
        QList<QVariant> measurementsList = data["measurements_2"].toList();

        // 使用 QSet 来存储唯一的通道
        QSet<QString> uniqueChannels;

        // 遍历 measurementsList，提取每个 measurement 的 channel 并添加到 QSet 中
        for (const QVariant &measurementVariant : measurementsList) {
            QVariantMap measurement = measurementVariant.toMap();
            QString channel = measurement["channel"].toString();
            uniqueChannels.insert(channel);
        }

        // 通道数即为 QSet 中元素的数量
        M = uniqueChannels.size();
        qDebug() << "Table1 Channel count:" << M;

        int rowIndex_2 = 2;

        // 该部分代码用来替换表格从TM开始
        // 假设 data 是包含 measurements 的 QVariantMap
        QVariantList measurements = data["measurements"].toList();

        // 创建一个临时存储新的 channel 值的列表
        QList<QString> newChannels;

        // 遍历 measurements，构建新的 channel 列表
        for (const QVariant &measurementVariant : measurements) {
            QVariantMap measurement = measurementVariant.toMap();
            QString currentChannel = measurement["channel"].toString();

            // 提取数字部分并增加
            bool ok;
            int channelNumber = currentChannel.mid(1).toInt(&ok); // 提取 T 后面的数字
            if (ok) {
                newChannels.append(QString("T%1").arg(channelNumber + M)); // 增加 M (读取表一的通道数)
            } else {
                newChannels.append(currentChannel); // 如果解析失败，保持原值
            }
        }

        // 在原始数据中替换 channel 值
        for (int i = 0; i < measurements.size(); ++i) {
            QVariantMap measurement = measurements[i].toMap();
            measurement["channel"] = newChannels[i]; // 更新 channel 为新的值
            measurements[i] = measurement; // 重新赋值回原列表
        }


        for (const QVariant &measurementVariant : measurements) {
            QVariantMap measurement = measurementVariant.toMap();

            for (int col = 1; col <= columnCount_2; ++col) {
                QAxObject* cell = newTable_2->querySubObject("Cell(Row, Column)", rowIndex_2, col);
                QAxObject* range = cell->querySubObject("Range");

                switch (col) {
                case 1: range->setProperty("Text", measurement["channel"].toString()); break;
                case 2: range->setProperty("Text", measurement["refRes"].toString()); break;
                case 3: range->setProperty("Text", measurement["measRes"].toString()); break;
                case 4: range->setProperty("Text", measurement["devRes"].toString()); break;
                case 5: range->setProperty("Text", QString::number(0.1, 'f', 1)); break;
                case 6:
                    double devValue = qAbs(measurement["devRes"].toString().toDouble());
                    range->setProperty("Text", devValue <= 0.1 ? "P" : "F"); //电压校准结果
                    break;
                }
                // 设置单元格样式
                setCellStyle(cell);
            }
            rowIndex_2++;
        }

        // 设置除表头外所有单元格的边框和字体
        for (int row = 2; row <= rowIndex_2; ++row) {
            for (int col = 1; col <= columnCount_2; ++col) {
                QAxObject* cell = newTable_2->querySubObject("Cell(Row, Column)", row, col);
                QAxObject* range = cell->querySubObject("Range");

                QAxObject* font = range->querySubObject("Font");
                font->setProperty("Name", "宋体");
                font->setProperty("Size", 9);
                font->setProperty("Bold", false);

                QAxObject* paragraphFormat = range->querySubObject("ParagraphFormat");
                paragraphFormat->setProperty("Alignment", 1);  // 1表示居中对齐

                QAxObject* borders = cell->querySubObject("Borders");
                for (int i = 1; i <= 4; ++i) {  // 1-4 表示上下左右边框
                    QAxObject* border = borders->querySubObject("Item(int)", i);
                    border->setProperty("LineStyle", 1);  // 1表示单实线
                }
            }
        }

        // 合并相同参考阻值的单元格
        mergeSameRefResCells(newTable_2, data["measurements"].toList());
    }
    else if (isTM228ND) {
        // 表2
        QStringList headers_2;

        headers_2 = QStringList({
            "通道",
            "参考阻值(" + data["refResUnit"].toString() + ")",
            "测量阻值(" + data["measResUnit"].toString() + ")",
            "阻值偏差(" + data["devUnit"].toString() + ")",
            "允许偏差(Ω)",
            "校准结果"
        });

        int numRows_2 = data["measurements"].toList().size();
        QAxObject* tables_2 = document->querySubObject("Tables");

        int columnCount_2 = 6;

        QAxObject* newTable_2 = tables_2->querySubObject("Add(QVariant, int, int)", range->asVariant(), numRows_2 + 1, columnCount_2);

        for (int i = 0; i < headers_2.size(); ++i) {
            QAxObject* cell = newTable_2->querySubObject("Cell(Row, Column)", 1, i + 1);
            QAxObject* range = cell->querySubObject("Range");
            range->setProperty("Text", headers_2[i]);

            QAxObject* font = range->querySubObject("Font");
            font->setProperty("Name", "宋体");
            font->setProperty("Size", 9);
            font->setProperty("Bold", true);

            QAxObject* borders = cell->querySubObject("Borders");
            for (int j = 1; j <= 4; ++j) {  // 1-4 表示上下左右边框
                QAxObject* border = borders->querySubObject("Item(int)", j);
                border->setProperty("LineStyle", 1);  // 1表示单实线
            }

            // 设置单元格内容居中
            QAxObject* paragraphFormat = range->querySubObject("ParagraphFormat");
            paragraphFormat->setProperty("Alignment", 1);  // 1表示居中对齐
        }

        int rowIndex_2 = 2;

        // 假设 data 是包含 measurements 的 QVariantMap
        QVariantList measurements = data["measurements"].toList();

        for (const QVariant &measurementVariant : measurements) {
            QVariantMap measurement = measurementVariant.toMap();

            for (int col = 1; col <= columnCount_2; ++col) {
                QAxObject* cell = newTable_2->querySubObject("Cell(Row, Column)", rowIndex_2, col);
                QAxObject* range = cell->querySubObject("Range");

                double refResValue = measurement["refRes"].toString().toDouble();
                double allowedDev = calculateAllowedDev("TM22XND-P", refResValue); // 计算TM228ND允差

                switch (col) {
                    case 1: range->setProperty("Text", measurement["channel"].toString()); break;
                    case 2: range->setProperty("Text", measurement["refRes"].toString()); break;
                    case 3: range->setProperty("Text", measurement["measRes"].toString()); break;
                    case 4: range->setProperty("Text", measurement["devRes"].toString()); break;
                    case 5:
                        range->setProperty("Text", QString::number(allowedDev, 'f', 2)); // 电阻允许偏差
                        break;
                    case 6:
                        double devValue = qAbs(measurement["devRes"].toString().toDouble());
                        range->setProperty("Text", devValue <= allowedDev ? "P" : "F"); // 电压校准结果
                        break;
                    // 设置单元格样式
                    setCellStyle(cell);
                }
            }
            rowIndex_2++;
        }

        // 设置除表头外所有单元格的边框和字体
        for (int row = 2; row <= rowIndex_2; ++row) {
            for (int col = 1; col <= columnCount_2; ++col) {
                QAxObject* cell = newTable_2->querySubObject("Cell(Row, Column)", row, col);
                QAxObject* range = cell->querySubObject("Range");

                QAxObject* font = range->querySubObject("Font");
                font->setProperty("Name", "宋体");
                font->setProperty("Size", 9);
                font->setProperty("Bold", false);

                QAxObject* paragraphFormat = range->querySubObject("ParagraphFormat");
                paragraphFormat->setProperty("Alignment", 1);  // 1表示居中对齐

                QAxObject* borders = cell->querySubObject("Borders");
                for (int i = 1; i <= 4; ++i) {  // 1-4 表示上下左右边框
                    QAxObject* border = borders->querySubObject("Item(int)", i);
                    border->setProperty("LineStyle", 1);  // 1表示单实线
                }
            }
        }

        // 合并相同参考阻值的单元格
        mergeSameRefResCells(newTable_2, data["measurements"].toList());

        // 移动range到表格末尾
        range = newTable_2->querySubObject("Range");
        range->dynamicCall("Collapse(int)", 0); // 0 表示移动到末尾

//        // 添加一个空行
//        range->dynamicCall("InsertParagraphAfter()");
//        range = range->querySubObject("Next(QVariant)", 1);

        // 插入重复性测试表格
        /*
        [
            [
                {
                    "resistanceValue": 5.0,
                    "resistanceUnit": "kΩ",
                    "samplingRate": "1Hz",
                    "stdDevValues": [0.1, 0.2, 0.3],  // 示例数据
                    "techRequirement": 0.5,  // 示例技术要求
                    "isQualified": true
                },
                {
                    "resistanceValue": 10.0,
                    "resistanceUnit": "kΩ",
                    "samplingRate": "1Hz",
                    "stdDevValues": [0.4, 0.5, 0.6],  // 示例数据
                    "techRequirement": 0.5,  // 示例技术要求
                    "isQualified": false
                }
            ],// 1Hz表
            [
                {
                    "resistanceValue": 5.0,
                    "resistanceUnit": "kΩ",
                    "samplingRate": "10Hz",
                    "stdDevValues": [0.1, 0.2, 0.3],  // 示例数据
                    "techRequirement": 0.5,  // 示例技术要求
                    "isQualified": true
                },
                {
                    "resistanceValue": 10.0,
                    "resistanceUnit": "kΩ",
                    "samplingRate": "10Hz",
                    "stdDevValues": [0.4, 0.5, 0.6],  // 示例数据
                    "techRequirement": 0.5,  // 示例技术要求
                    "isQualified": false
                }
            ]
        ]
        */
        // 获取通道数量
        int repeatTestCHCount = data["channelCount"].toInt();  // 假设数据中包含通道数量

        QString testDescription;

        switch (repeatTestCHCount) {
        case 2:
            testDescription = "重复性测试：将TM222ND-P连接至对应阻值的标准电阻，连续读取10分钟的读数，结束后计算每个通道的3倍标准差即3σ，并将其转化等效温度偏差。将等效温度偏差与对应的技术要求比较，满足要求即为合格，不满足则为不合格。";
            break;
        case 4:
            testDescription = "重复性测试：将TM224ND-P连接至对应阻值的标准电阻，连续读取10分钟的读数，结束后计算每个通道的3倍标准差即3σ，并将其转化等效温度偏差。将等效温度偏差与对应的技术要求比较，满足要求即为合格，不满足则为不合格。";
            break;
        case 8:
            testDescription = "重复性测试：将TM228ND-P连接至对应阻值的标准电阻，连续读取10分钟的读数，结束后计算每个通道的3倍标准差即3σ，并将其转化等效温度偏差。将等效温度偏差与对应的技术要求比较，满足要求即为合格，不满足则为不合格。";
            break;
        default:
            testDescription = "重复性测试：将TM22XND-P连接至对应阻值的标准电阻，连续读取10分钟的读数，结束后计算每个通道的3倍标准差即3σ，并将其转化等效温度偏差。将等效温度偏差与对应的技术要求比较，满足要求即为合格，不满足则为不合格。";
            break;
        }

        // 动态生成表头
        QStringList headers_3;
        headers_3.append("测试阻值");
        // 动态添加通道列
        for (int i = 1; i <= repeatTestCHCount; ++i) {
            headers_3.append(QString("3σ@ %1").arg(i, 2, 10, QChar('0')));  // 使用02这样的格式
        }
        headers_3.append("技术要求(mK)");
        headers_3.append("结论");

        QVariantList combinedResults = data["repeatTestTableData"].toList();
        // QVariantList results1Hz = combinedResults[0].toList();
        // QVariantList results10Hz = combinedResults[1].toList();

        range = createTestTable(document, range, headers_3, combinedResults, repeatTestCHCount);

        // 添加段落
        QAxObject* paragraphs = document->querySubObject("Paragraphs");
        QAxObject* newParagraph = paragraphs->querySubObject("Add(QVariant)", range->asVariant());
        range = newParagraph->querySubObject("Range");
        range->setProperty("Text", testDescription);

        // 设置字体为宋体，10号，常规（不加粗）
        QAxObject* font = range->querySubObject("Font");
        if (font) {
            font->setProperty("Name", "宋体");
            font->setProperty("Size", 10);
            font->setProperty("Bold", false);
            delete font;
        }

        // 设置左对齐
        QAxObject* titleFormat = range->querySubObject("ParagraphFormat");
        if (titleFormat) {
            titleFormat->setProperty("Alignment", 0); // 0 = wdAlignParagraphLeft

            // 设置段前间距为0.5行
            titleFormat->setProperty("LineSpacingRule", 0); // 0 = wdLineSpaceSingle
            titleFormat->setProperty("SpaceBefore", 6); // 6磅 = 0.5行

            delete titleFormat;
        }

        range->dynamicCall("Collapse(int)", 0); // 0 表示移动到末尾

        // 添加一个空行
        range->dynamicCall("InsertParagraphAfter()");
        range = range->querySubObject("Next(QVariant)", 1);
    }
    else if (isTM18ND) {
        // 表2
        QStringList headers_2;

        headers_2 = QStringList({
            "通道",
            "参考阻值(" + data["refResUnit"].toString() + ")",
            "测量阻值(" + data["measResUnit"].toString() + ")",
            "阻值偏差(" + data["devUnit"].toString() + ")",
            "允许偏差(Ω)",
            "校准结果"
        });

        int numRows_2 = data["measurements"].toList().size();
        QAxObject* tables_2 = document->querySubObject("Tables");

        int columnCount_2 = 6;

        QAxObject* newTable_2 = tables_2->querySubObject("Add(QVariant, int, int)", range->asVariant(), numRows_2 + 1, columnCount_2);

        for (int i = 0; i < headers_2.size(); ++i) {
            QAxObject* cell = newTable_2->querySubObject("Cell(Row, Column)", 1, i + 1);
            QAxObject* range = cell->querySubObject("Range");
            range->setProperty("Text", headers_2[i]);

            QAxObject* font = range->querySubObject("Font");
            font->setProperty("Name", "宋体");
            font->setProperty("Size", 9);
            font->setProperty("Bold", true);

            QAxObject* borders = cell->querySubObject("Borders");
            for (int j = 1; j <= 4; ++j) {  // 1-4 表示上下左右边框
                QAxObject* border = borders->querySubObject("Item(int)", j);
                border->setProperty("LineStyle", 1);  // 1表示单实线
            }

            // 设置单元格内容居中
            QAxObject* paragraphFormat = range->querySubObject("ParagraphFormat");
            paragraphFormat->setProperty("Alignment", 1);  // 1表示居中对齐
        }

        int rowIndex_2 = 2;

        // 该部分代码用来替换表格从T1开始->T2开始
        // 假设 data 是包含 measurements 的 QVariantMap
        QVariantList measurements = data["measurements"].toList();

        for (const QVariant &measurementVariant : measurements) {
            QVariantMap measurement = measurementVariant.toMap();

            for (int col = 1; col <= columnCount_2; ++col) {
                QAxObject* cell = newTable_2->querySubObject("Cell(Row, Column)", rowIndex_2, col);
                QAxObject* range = cell->querySubObject("Range");

                double refResValue = measurement["refRes"].toString().toDouble();
                double allowedDev = calculateAllowedDev("TM18ND", refResValue); //

                switch (col) {
                    case 1: range->setProperty("Text", measurement["channel"].toString()); break;
                    case 2: range->setProperty("Text", measurement["refRes"].toString()); break;
                    case 3: range->setProperty("Text", measurement["measRes"].toString()); break;
                    case 4: range->setProperty("Text", measurement["devRes"].toString()); break;
                    case 5:
                        range->setProperty("Text", QString::number(allowedDev, 'f', 1)); // 电阻允许偏差
                        break;
                    case 6:
                        double devValue = qAbs(measurement["devRes"].toString().toDouble());
                        range->setProperty("Text", devValue <= allowedDev ? "P" : "F"); // 电压校准结果
                        break;
                    // 设置单元格样式
                    setCellStyle(cell);
                }
            }
            rowIndex_2++;
        }

        // 设置除表头外所有单元格的边框和字体
        for (int row = 2; row <= rowIndex_2; ++row) {
            for (int col = 1; col <= columnCount_2; ++col) {
                QAxObject* cell = newTable_2->querySubObject("Cell(Row, Column)", row, col);
                QAxObject* range = cell->querySubObject("Range");

                QAxObject* font = range->querySubObject("Font");
                font->setProperty("Name", "宋体");
                font->setProperty("Size", 9);
                font->setProperty("Bold", false);

                QAxObject* paragraphFormat = range->querySubObject("ParagraphFormat");
                paragraphFormat->setProperty("Alignment", 1);  // 1表示居中对齐

                QAxObject* borders = cell->querySubObject("Borders");
                for (int i = 1; i <= 4; ++i) {  // 1-4 表示上下左右边框
                    QAxObject* border = borders->querySubObject("Item(int)", i);
                    border->setProperty("LineStyle", 1);  // 1表示单实线
                }
            }
        }

        // 合并相同参考阻值的单元格
        mergeSameRefResCells(newTable_2, data["measurements"].toList());
    }
    else if (isTM18RD_P) {
        // 表2
        QStringList headers_2;

        headers_2 = QStringList({
            "通道",
            "参考阻值(" + data["refResUnit"].toString() + ")",
            "测量阻值(" + data["measResUnit"].toString() + ")",
            "阻值偏差(" + data["devUnit"].toString() + ")",
            "允许偏差(Ω)",
            "校准结果"
        });

        int numRows_2 = data["measurements"].toList().size();
        QAxObject* tables_2 = document->querySubObject("Tables");

        int columnCount_2 = 6;

        QAxObject* newTable_2 = tables_2->querySubObject("Add(QVariant, int, int)", range->asVariant(), numRows_2 + 1, columnCount_2);

        for (int i = 0; i < headers_2.size(); ++i) {
            QAxObject* cell = newTable_2->querySubObject("Cell(Row, Column)", 1, i + 1);
            QAxObject* range = cell->querySubObject("Range");
            range->setProperty("Text", headers_2[i]);

            QAxObject* font = range->querySubObject("Font");
            font->setProperty("Name", "宋体");
            font->setProperty("Size", 9);
            font->setProperty("Bold", true);

            QAxObject* borders = cell->querySubObject("Borders");
            for (int j = 1; j <= 4; ++j) {  // 1-4 表示上下左右边框
                QAxObject* border = borders->querySubObject("Item(int)", j);
                border->setProperty("LineStyle", 1);  // 1表示单实线
            }

            // 设置单元格内容居中
            QAxObject* paragraphFormat = range->querySubObject("ParagraphFormat");
            paragraphFormat->setProperty("Alignment", 1);  // 1表示居中对齐
        }

        int rowIndex_2 = 2;

        QVariantList measurements = data["measurements"].toList();

        for (const QVariant &measurementVariant : measurements) {
            QVariantMap measurement = measurementVariant.toMap();

            for (int col = 1; col <= columnCount_2; ++col) {
                QAxObject* cell = newTable_2->querySubObject("Cell(Row, Column)", rowIndex_2, col);
                QAxObject* range = cell->querySubObject("Range");

                double refResValue = measurement["refRes"].toString().toDouble();
                double allowedDev = calculateAllowedDev("TM18RD-P", refResValue); //

                switch (col) {
                    case 1: range->setProperty("Text", measurement["channel"].toString()); break;
                    case 2: range->setProperty("Text", measurement["refRes"].toString()); break;
                    case 3: range->setProperty("Text", measurement["measRes"].toString()); break;
                    case 4: range->setProperty("Text", measurement["devRes"].toString()); break;
                    case 5:
                        range->setProperty("Text", QString::number(allowedDev, 'f', 2)); // 电阻允许偏差
                        break;
                    case 6:
                        double devValue = qAbs(measurement["devRes"].toString().toDouble());
                        range->setProperty("Text", devValue <= allowedDev ? "P" : "F"); // 电压校准结果
                        break;
                    // 设置单元格样式
                    setCellStyle(cell);
                }
            }
            rowIndex_2++;
        }

        // 设置除表头外所有单元格的边框和字体
        for (int row = 2; row <= rowIndex_2; ++row) {
            for (int col = 1; col <= columnCount_2; ++col) {
                QAxObject* cell = newTable_2->querySubObject("Cell(Row, Column)", row, col);
                QAxObject* range = cell->querySubObject("Range");

                QAxObject* font = range->querySubObject("Font");
                font->setProperty("Name", "宋体");
                font->setProperty("Size", 9);
                font->setProperty("Bold", false);

                QAxObject* paragraphFormat = range->querySubObject("ParagraphFormat");
                paragraphFormat->setProperty("Alignment", 1);  // 1表示居中对齐

                QAxObject* borders = cell->querySubObject("Borders");
                for (int i = 1; i <= 4; ++i) {  // 1-4 表示上下左右边框
                    QAxObject* border = borders->querySubObject("Item(int)", i);
                    border->setProperty("LineStyle", 1);  // 1表示单实线
                }
            }
        }

        // 合并相同参考阻值的单元格
        mergeSameRefResCells(newTable_2, data["measurements"].toList());
    }
    else if(is618A_TC) {
        // 表一
        QStringList headers_1;
        headers_1 = QStringList({
            "通道",
            "冷端参考阻值(" + data["refResUnit"].toString() + ")",
            "测量阻值(" + data["measResUnit"].toString() + ")",
            "阻值偏差(Ω)",
            "等效温度偏差(mK)",
            "允许偏差(mK)",
            "校准结果"
        });

        int numRows_1 = data["measurements_2"].toList().size();
        QAxObject* tables_1 = document->querySubObject("Tables");

        int columnCount_1 = 7;

        QAxObject* newTable_1 = tables_1->querySubObject("Add(QVariant, int, int)", range->asVariant(), numRows_1 + 1, columnCount_1);



        for (int i = 0; i < headers_1.size(); ++i) {
            QAxObject* cell = newTable_1->querySubObject("Cell(Row, Column)", 1, i + 1);
            QAxObject* range = cell->querySubObject("Range");
            range->setProperty("Text", headers_1[i]);

            QAxObject* font = range->querySubObject("Font");
            font->setProperty("Name", "宋体");
            font->setProperty("Size", 9);
            font->setProperty("Bold", true);

            QAxObject* borders = cell->querySubObject("Borders");
            for (int j = 1; j <= 4; ++j) {  // 1-4 表示上下左右边框
                QAxObject* border = borders->querySubObject("Item(int)", j);
                border->setProperty("LineStyle", 1);  // 1表示单实线
            }

            // 设置单元格内容居中
            QAxObject* paragraphFormat = range->querySubObject("ParagraphFormat");
            paragraphFormat->setProperty("Alignment", 1);  // 1表示居中对齐
        }

        int rowIndex = 2;

        for (const QVariant &measurementVariant : data["measurements_2"].toList()) {
            QVariantMap measurement = measurementVariant.toMap();

            for (int col = 1; col <= columnCount_1; ++col) {
                QAxObject* cell = newTable_1->querySubObject("Cell(Row, Column)", rowIndex, col);
                QAxObject* range = cell->querySubObject("Range");

                switch (col) {
                    case 1: range->setProperty("Text", measurement["channel"].toString()); break;
                    case 2: range->setProperty("Text", measurement["refRes"].toString()); break;
                    case 3: range->setProperty("Text", measurement["measRes"].toString()); break;
                    case 4: range->setProperty("Text", measurement["devRes"].toString()); break;
                    case 5: range->setProperty("Text", calculateTempDev(measurement, data)); break;
                    case 6: {
                        QString deviceModel = data["deviceModel"].toString();
                        double refResValue = measurement["refRes"].toString().toDouble();
                        double allowedDev = calculateAllowedDev(deviceModel, refResValue);
                        range->setProperty("Text", QString::number(allowedDev, 'f', 1)); // 电阻允许偏差
                        break;
                    }
                    case 7:{
                        double tempDev = calculateTempDev(measurement, data).toDouble();
                        QString deviceModel = data["deviceModel"].toString();
                        double refResValue = measurement["refRes"].toString().toDouble();
                        double allowedDev = calculateAllowedDev(deviceModel, refResValue);
                        range->setProperty("Text", qAbs(tempDev) <= allowedDev ? "P" : "F");
                        break;
                    }
                }
                // 设置单元格样式
                setCellStyle(cell);
            }
            rowIndex++;
        }

        // 设置除表头外所有单元格的边框和字体
        for (int row = 2; row <= rowIndex; ++row) {
            for (int col = 1; col <= columnCount_1; ++col) {
                QAxObject* cell = newTable_1->querySubObject("Cell(Row, Column)", row, col);
                QAxObject* range = cell->querySubObject("Range");

                QAxObject* font = range->querySubObject("Font");
                font->setProperty("Name", "宋体");
                font->setProperty("Size", 9);
                font->setProperty("Bold", false);

                QAxObject* paragraphFormat = range->querySubObject("ParagraphFormat");
                paragraphFormat->setProperty("Alignment", 1);  // 1表示居中对齐

                QAxObject* borders = cell->querySubObject("Borders");
                for (int i = 1; i <= 4; ++i) {  // 1-4 表示上下左右边框
                    QAxObject* border = borders->querySubObject("Item(int)", i);
                    border->setProperty("LineStyle", 1);  // 1表示单实线
                }
            }
        }

        // 合并相同参考阻值的单元格
        mergeSameRefResCells(newTable_1, data["measurements_2"].toList());

        // 移动光标到第一个表格的末尾
        range = newTable_1->querySubObject("Range()");
        range->dynamicCall("Collapse(int)", 0); // 0表示光标移动到范围的末尾

        // 插入一个空行
        range->dynamicCall("InsertParagraphAfter()");

        // 移动光标到新插入的段落末尾
        range = range->querySubObject("Paragraphs(1)")->querySubObject("Range()");
        range->dynamicCall("Collapse(int)", 0); // 0表示光标移动到范围的末尾

        // 表2
        QStringList headers_2;
        headers_2 = QStringList({"通道", "参考电压(mV)", "测量电压(mV)", "电压偏差(mV)", "允许偏差(mV)", "校准结果"});

        int numRows_2 = data["measurements"].toList().size();
        QAxObject* tables_2 = document->querySubObject("Tables");

        int columnCount_2 = 6;

        QAxObject* newTable_2 = tables_2->querySubObject("Add(QVariant, int, int)", range->asVariant(), numRows_2 + 1, columnCount_2);


        for (int i = 0; i < headers_2.size(); ++i) {
            QAxObject* cell = newTable_2->querySubObject("Cell(Row, Column)", 1, i + 1);
            QAxObject* range = cell->querySubObject("Range");
            range->setProperty("Text", headers_2[i]);

            QAxObject* font = range->querySubObject("Font");
            font->setProperty("Name", "宋体");
            font->setProperty("Size", 9);
            font->setProperty("Bold", true);

            QAxObject* borders = cell->querySubObject("Borders");
            for (int j = 1; j <= 4; ++j) {  // 1-4 表示上下左右边框
                QAxObject* border = borders->querySubObject("Item(int)", j);
                border->setProperty("LineStyle", 1);  // 1表示单实线
            }

            // 设置单元格内容居中
            QAxObject* paragraphFormat = range->querySubObject("ParagraphFormat");
            paragraphFormat->setProperty("Alignment", 1);  // 1表示居中对齐
        }

        int rowIndex_2 = 2;

        for (const QVariant &measurementVariant : data["measurements"].toList()) {
            QVariantMap measurement = measurementVariant.toMap();

            for (int col = 1; col <= columnCount_2; ++col) {
                QAxObject* cell = newTable_2->querySubObject("Cell(Row, Column)", rowIndex_2, col);
                QAxObject* range = cell->querySubObject("Range");

                switch (col) {
                    case 1: range->setProperty("Text", measurement["channel"].toString()); break;
                    case 2: range->setProperty("Text", measurement["refRes"].toString()); break;
                    case 3: range->setProperty("Text", measurement["measRes"].toString()); break;
                    case 4: range->setProperty("Text", measurement["devRes"].toString()); break;
                    case 5: {
                            double measValue = measurement["measRes"].toString().toDouble();
                            double allowedDev = 0.0001 * measValue + 0.01;  //此处的measValue的基本单位为mV计算（与表中传入数据一致，压力通道单位则按V处理）
                            range->setProperty("Text", QString::number(allowedDev, 'f', 3)); //计算电压允许偏差值（mV）
                            break;
                        }
                    case 6: {
                            double devValue = qAbs(measurement["devRes"].toString().toDouble());
                            double measValue = measurement["measRes"].toString().toDouble();
                            double allowedDev = 0.0001 * measValue + 0.01;
                            range->setProperty("Text", devValue <= allowedDev ? "P" : "F"); //电压校准结果
                            break;
                        }
                }
                // 设置单元格样式
                setCellStyle(cell);
            }
             rowIndex_2++;
        }


        // 设置除表头外所有单元格的边框和字体
        for (int row = 2; row <= rowIndex_2; ++row) {
            for (int col = 1; col <= columnCount_2; ++col) {
                QAxObject* cell = newTable_2->querySubObject("Cell(Row, Column)", row, col);
                QAxObject* range = cell->querySubObject("Range");

                QAxObject* font = range->querySubObject("Font");
                font->setProperty("Name", "宋体");
                font->setProperty("Size", 9);
                font->setProperty("Bold", false);

                QAxObject* paragraphFormat = range->querySubObject("ParagraphFormat");
                paragraphFormat->setProperty("Alignment", 1);  // 1表示居中对齐

                QAxObject* borders = cell->querySubObject("Borders");
                for (int i = 1; i <= 4; ++i) {  // 1-4 表示上下左右边框
                    QAxObject* border = borders->querySubObject("Item(int)", i);
                    border->setProperty("LineStyle", 1);  // 1表示单实线
                }
            }
        }

        // 合并相同参考阻值的单元格
        mergeSameRefResCells(newTable_2, data["measurements"].toList());
    }
    else if(is618A_TC_12) {
        // 表2
        QStringList headers_2;
        headers_2 = QStringList({"通道", "参考电压(mV)", "测量电压(mV)", "电压偏差(mV)", "允许偏差(mV)", "校准结果"});

        int numRows_2 = data["measurements"].toList().size();
        QAxObject* tables_2 = document->querySubObject("Tables");

        int columnCount_2 = 6;

        QAxObject* newTable_2 = tables_2->querySubObject("Add(QVariant, int, int)", range->asVariant(), numRows_2 + 1, columnCount_2);


        for (int i = 0; i < headers_2.size(); ++i) {
            QAxObject* cell = newTable_2->querySubObject("Cell(Row, Column)", 1, i + 1);
            QAxObject* range = cell->querySubObject("Range");
            range->setProperty("Text", headers_2[i]);

            QAxObject* font = range->querySubObject("Font");
            font->setProperty("Name", "宋体");
            font->setProperty("Size", 9);
            font->setProperty("Bold", true);

            QAxObject* borders = cell->querySubObject("Borders");
            for (int j = 1; j <= 4; ++j) {  // 1-4 表示上下左右边框
                QAxObject* border = borders->querySubObject("Item(int)", j);
                border->setProperty("LineStyle", 1);  // 1表示单实线
            }

            // 设置单元格内容居中
            QAxObject* paragraphFormat = range->querySubObject("ParagraphFormat");
            paragraphFormat->setProperty("Alignment", 1);  // 1表示居中对齐
        }

        int rowIndex_2 = 2;

        for (const QVariant &measurementVariant : data["measurements"].toList()) {
            QVariantMap measurement = measurementVariant.toMap();

            for (int col = 1; col <= columnCount_2; ++col) {
                QAxObject* cell = newTable_2->querySubObject("Cell(Row, Column)", rowIndex_2, col);
                QAxObject* range = cell->querySubObject("Range");

                switch (col) {
                    case 1: range->setProperty("Text", measurement["channel"].toString()); break;
                    case 2: range->setProperty("Text", measurement["refRes"].toString()); break;
                    case 3: range->setProperty("Text", measurement["measRes"].toString()); break;
                    case 4: range->setProperty("Text", measurement["devRes"].toString()); break;
                    case 5: {
                            double measValue = measurement["measRes"].toString().toDouble();
                            double allowedDev = 0.0001 * measValue + 0.01;  //此处的measValue的基本单位为mV计算（与表中传入数据一致，压力通道单位则按V处理）
                            range->setProperty("Text", QString::number(allowedDev, 'f', 3)); //计算电压允许偏差值（mV）
                            break;
                        }
                    case 6: {
                            double devValue = qAbs(measurement["devRes"].toString().toDouble());
                            double measValue = measurement["measRes"].toString().toDouble();
                            double allowedDev = 0.0001 * measValue + 0.01;
                            range->setProperty("Text", devValue <= allowedDev ? "P" : "F"); //电压校准结果
                            break;
                        }
                }
                // 设置单元格样式
                setCellStyle(cell);
            }
             rowIndex_2++;
        }


        // 设置除表头外所有单元格的边框和字体
        for (int row = 2; row <= rowIndex_2; ++row) {
            for (int col = 1; col <= columnCount_2; ++col) {
                QAxObject* cell = newTable_2->querySubObject("Cell(Row, Column)", row, col);
                QAxObject* range = cell->querySubObject("Range");

                QAxObject* font = range->querySubObject("Font");
                font->setProperty("Name", "宋体");
                font->setProperty("Size", 9);
                font->setProperty("Bold", false);

                QAxObject* paragraphFormat = range->querySubObject("ParagraphFormat");
                paragraphFormat->setProperty("Alignment", 1);  // 1表示居中对齐

                QAxObject* borders = cell->querySubObject("Borders");
                for (int i = 1; i <= 4; ++i) {  // 1-4 表示上下左右边框
                    QAxObject* border = borders->querySubObject("Item(int)", i);
                    border->setProperty("LineStyle", 1);  // 1表示单实线
                }
            }
        }

        // 合并相同参考阻值的单元格
        mergeSameRefResCells(newTable_2, data["measurements"].toList());
    }
    else if(is1611A_TC || is1611A_VS) {
        // 压力电压表 3
        QStringList headers_3;
        headers_3 = QStringList({"通道", "压力电压(V)", "测量电压(V)", "电压偏差(V)", "允许偏差(V)", "校准结果"});

        int numRows_3 = data["measurements_3"].toList().size();
        QAxObject* tables_3 = document->querySubObject("Tables");

        int columnCount_3 = 6;

        QAxObject* newTable_3 = tables_3->querySubObject("Add(QVariant, int, int)", range->asVariant(), numRows_3 + 1, columnCount_3);

        for (int i = 0; i < headers_3.size(); ++i) {
            QAxObject* cell = newTable_3->querySubObject("Cell(Row, Column)", 1, i + 1);
            QAxObject* range = cell->querySubObject("Range");
            range->setProperty("Text", headers_3[i]);

            QAxObject* font = range->querySubObject("Font");
            font->setProperty("Name", "宋体");
            font->setProperty("Size", 9);
            font->setProperty("Bold", true);

            QAxObject* borders = cell->querySubObject("Borders");
            for (int j = 1; j <= 4; ++j) {  // 1-4 表示上下左右边框
                QAxObject* border = borders->querySubObject("Item(int)", j);
                border->setProperty("LineStyle", 1);  // 1表示单实线
            }

            // 设置单元格内容居中
            QAxObject* paragraphFormat = range->querySubObject("ParagraphFormat");
            paragraphFormat->setProperty("Alignment", 1);  // 1表示居中对齐
        }

        int rowIndex_3 = 2;

        for (const QVariant &measurementVariant : data["measurements_3"].toList()) {
            QVariantMap measurement = measurementVariant.toMap();

            for (int col = 1; col <= columnCount_3; ++col) {
                QAxObject* cell = newTable_3->querySubObject("Cell(Row, Column)", rowIndex_3, col);
                QAxObject* range = cell->querySubObject("Range");

                switch (col) {
                case 1: range->setProperty("Text", measurement["channel"].toString()); break;
                case 2: range->setProperty("Text", measurement["refRes"].toString()); break;
                case 3: range->setProperty("Text", measurement["measRes"].toString()); break;
                case 4: range->setProperty("Text", measurement["devRes"].toString()); break;
                case 5: {
                    double measValue = measurement["measRes"].toString().toDouble();
                    double allowedDev = 0.0001 * measValue + 0.001;  // 压力电压 measValue单位为V
                    range->setProperty("Text", QString::number(allowedDev, 'f', 5)); //计算电压允许偏差值（V）
                    break;
                }
                case 6: {
                    double devValue = qAbs(measurement["devRes"].toString().toDouble());
                    double measValue = measurement["measRes"].toString().toDouble();
                    double allowedDev = 0.0001 * measValue + 0.001;
                    range->setProperty("Text", devValue <= allowedDev ? "P" : "F"); //电压校准结果
                    break;
                }
                }
                // 设置单元格样式
                setCellStyle(cell);
            }
            rowIndex_3++;
        }

        // 设置除表头外所有单元格的边框和字体
        for (int row = 2; row <= rowIndex_3; ++row) {
            for (int col = 1; col <= columnCount_3; ++col) {
                QAxObject* cell = newTable_3->querySubObject("Cell(Row, Column)", row, col);
                QAxObject* range = cell->querySubObject("Range");

                QAxObject* font = range->querySubObject("Font");
                font->setProperty("Name", "宋体");
                font->setProperty("Size", 9);
                font->setProperty("Bold", false);

                QAxObject* paragraphFormat = range->querySubObject("ParagraphFormat");
                paragraphFormat->setProperty("Alignment", 1);  // 1表示居中对齐

                QAxObject* borders = cell->querySubObject("Borders");
                for (int i = 1; i <= 4; ++i) {  // 1-4 表示上下左右边框
                    QAxObject* border = borders->querySubObject("Item(int)", i);
                    border->setProperty("LineStyle", 1);  // 1表示单实线
                }
            }
        }

        // 合并相同参考阻值的单元格
        mergeSameRefResCells(newTable_3, data["measurements_3"].toList());

        // 移动光标到第一个表格的末尾
        range = newTable_3->querySubObject("Range()");
        range->dynamicCall("Collapse(int)", 0); // 0表示光标移动到范围的末尾

//        // 插入表名并设置居中对齐
//        range->dynamicCall("InsertAfter(const QString&)", "表一");
//        range->querySubObject("ParagraphFormat")->setProperty("Alignment", 1); // 1表示居中对齐

        // 插入一个空行
        range->dynamicCall("InsertParagraphAfter()");

        //setParagraphFormat(range);

        // 移动光标到新插入的段落末尾
        range = range->querySubObject("Paragraphs(1)")->querySubObject("Range()");
        range->dynamicCall("Collapse(int)", 0); // 0表示光标移动到范围的末尾


        // 表一
        QStringList headers_1;
        headers_1 = QStringList({
            "通道",
            "冷端参考阻值(" + data["refResUnit"].toString() + ")",
            "测量阻值(" + data["measResUnit"].toString() + ")",
            "阻值偏差(Ω)",
            "等效温度偏差(mK)",
            "允许偏差(mK)",
            "校准结果"
        });

        int numRows_1 = data["measurements_2"].toList().size();
        QAxObject* tables_1 = document->querySubObject("Tables");

        int columnCount_1 = 7;

        QAxObject* newTable_1 = tables_1->querySubObject("Add(QVariant, int, int)", range->asVariant(), numRows_1 + 1, columnCount_1);



        for (int i = 0; i < headers_1.size(); ++i) {
            QAxObject* cell = newTable_1->querySubObject("Cell(Row, Column)", 1, i + 1);
            QAxObject* range = cell->querySubObject("Range");
            range->setProperty("Text", headers_1[i]);

            QAxObject* font = range->querySubObject("Font");
            font->setProperty("Name", "宋体");
            font->setProperty("Size", 9);
            font->setProperty("Bold", true);

            QAxObject* borders = cell->querySubObject("Borders");
            for (int j = 1; j <= 4; ++j) {  // 1-4 表示上下左右边框
                QAxObject* border = borders->querySubObject("Item(int)", j);
                border->setProperty("LineStyle", 1);  // 1表示单实线
            }

            // 设置单元格内容居中
            QAxObject* paragraphFormat = range->querySubObject("ParagraphFormat");
            paragraphFormat->setProperty("Alignment", 1);  // 1表示居中对齐
        }

        int rowIndex = 2;

        for (const QVariant &measurementVariant : data["measurements_2"].toList()) {
            QVariantMap measurement = measurementVariant.toMap();

            for (int col = 1; col <= columnCount_1; ++col) {
                QAxObject* cell = newTable_1->querySubObject("Cell(Row, Column)", rowIndex, col);
                QAxObject* range = cell->querySubObject("Range");

                switch (col) {
                    case 1: range->setProperty("Text", measurement["channel"].toString()); break;
                    case 2: range->setProperty("Text", measurement["refRes"].toString()); break;
                    case 3: range->setProperty("Text", measurement["measRes"].toString()); break;
                    case 4: range->setProperty("Text", measurement["devRes"].toString()); break;
                    case 5: range->setProperty("Text", calculateTempDev(measurement, data)); break;
                    case 6: {
                        QString deviceModel = data["deviceModel"].toString();
                        double refResValue = measurement["refRes"].toString().toDouble();
                        double allowedDev = calculateAllowedDev(deviceModel, refResValue);
                        range->setProperty("Text", QString::number(allowedDev, 'f', 1)); // 电阻允许偏差
                        break;
                    }
                    case 7:{
                        double tempDev = calculateTempDev(measurement, data).toDouble();
                        QString deviceModel = data["deviceModel"].toString();
                        double refResValue = measurement["refRes"].toString().toDouble();
                        double allowedDev = calculateAllowedDev(deviceModel, refResValue);
                        range->setProperty("Text", qAbs(tempDev) <= allowedDev ? "P" : "F");
                        break;
                    }
                }
                // 设置单元格样式
                setCellStyle(cell);
            }
            rowIndex++;
        }

        // 设置除表头外所有单元格的边框和字体
        for (int row = 2; row <= rowIndex; ++row) {
            for (int col = 1; col <= columnCount_1; ++col) {
                QAxObject* cell = newTable_1->querySubObject("Cell(Row, Column)", row, col);
                QAxObject* range = cell->querySubObject("Range");

                QAxObject* font = range->querySubObject("Font");
                font->setProperty("Name", "宋体");
                font->setProperty("Size", 9);
                font->setProperty("Bold", false);

                QAxObject* paragraphFormat = range->querySubObject("ParagraphFormat");
                paragraphFormat->setProperty("Alignment", 1);  // 1表示居中对齐

                QAxObject* borders = cell->querySubObject("Borders");
                for (int i = 1; i <= 4; ++i) {  // 1-4 表示上下左右边框
                    QAxObject* border = borders->querySubObject("Item(int)", i);
                    border->setProperty("LineStyle", 1);  // 1表示单实线
                }
            }
        }

        // 合并相同参考阻值的单元格
        mergeSameRefResCells(newTable_1, data["measurements_2"].toList());

        // 移动光标到第一个表格的末尾
        range = newTable_1->querySubObject("Range()");
        range->dynamicCall("Collapse(int)", 0); // 0表示光标移动到范围的末尾

        // 插入一个空行
        range->dynamicCall("InsertParagraphAfter()");

        //setParagraphFormat(range);

        // 移动光标到新插入的段落末尾
        range = range->querySubObject("Paragraphs(1)")->querySubObject("Range()");
        range->dynamicCall("Collapse(int)", 0); // 0表示光标移动到范围的末尾

        // 表2
        QStringList headers_2;
        headers_2 = QStringList({"通道", "参考电压(mV)", "测量电压(mV)", "电压偏差(mV)", "允许偏差(mV)", "校准结果"});

        int numRows_2 = data["measurements"].toList().size();
        QAxObject* tables_2 = document->querySubObject("Tables");

        int columnCount_2 = 6;

        QAxObject* newTable_2 = tables_2->querySubObject("Add(QVariant, int, int)", range->asVariant(), numRows_2 + 1, columnCount_2);


        for (int i = 0; i < headers_2.size(); ++i) {
            QAxObject* cell = newTable_2->querySubObject("Cell(Row, Column)", 1, i + 1);
            QAxObject* range = cell->querySubObject("Range");
            range->setProperty("Text", headers_2[i]);

            QAxObject* font = range->querySubObject("Font");
            font->setProperty("Name", "宋体");
            font->setProperty("Size", 9);
            font->setProperty("Bold", true);

            QAxObject* borders = cell->querySubObject("Borders");
            for (int j = 1; j <= 4; ++j) {  // 1-4 表示上下左右边框
                QAxObject* border = borders->querySubObject("Item(int)", j);
                border->setProperty("LineStyle", 1);  // 1表示单实线
            }

            // 设置单元格内容居中
            QAxObject* paragraphFormat = range->querySubObject("ParagraphFormat");
            paragraphFormat->setProperty("Alignment", 1);  // 1表示居中对齐
        }

        int rowIndex_2 = 2;

        for (const QVariant &measurementVariant : data["measurements"].toList()) {
            QVariantMap measurement = measurementVariant.toMap();

            for (int col = 1; col <= columnCount_2; ++col) {
                QAxObject* cell = newTable_2->querySubObject("Cell(Row, Column)", rowIndex_2, col);
                QAxObject* range = cell->querySubObject("Range");

                switch (col) {
                    case 1: range->setProperty("Text", measurement["channel"].toString()); break;
                    case 2: range->setProperty("Text", measurement["refRes"].toString()); break;
                    case 3: range->setProperty("Text", measurement["measRes"].toString()); break;
                    case 4: range->setProperty("Text", measurement["devRes"].toString()); break;
                    case 5: {
                            double measValue = measurement["measRes"].toString().toDouble();
                            double allowedDev = 0.0001 * measValue + 0.01;
                            range->setProperty("Text", QString::number(allowedDev, 'f', 3)); //计算电压允许偏差值（mV）
                            break;
                        }
                    case 6: {
                            double devValue = qAbs(measurement["devRes"].toString().toDouble());
                            double measValue = measurement["measRes"].toString().toDouble();
                            double allowedDev = 0.0001 * measValue + 0.01;
                            range->setProperty("Text", devValue <= allowedDev ? "P" : "F"); //电压校准结果
                            break;
                        }
                }
                // 设置单元格样式
                setCellStyle(cell);
            }
             rowIndex_2++;
        }


        // 设置除表头外所有单元格的边框和字体
        for (int row = 2; row <= rowIndex_2; ++row) {
            for (int col = 1; col <= columnCount_2; ++col) {
                QAxObject* cell = newTable_2->querySubObject("Cell(Row, Column)", row, col);
                QAxObject* range = cell->querySubObject("Range");

                QAxObject* font = range->querySubObject("Font");
                font->setProperty("Name", "宋体");
                font->setProperty("Size", 9);
                font->setProperty("Bold", false);

                QAxObject* paragraphFormat = range->querySubObject("ParagraphFormat");
                paragraphFormat->setProperty("Alignment", 1);  // 1表示居中对齐

                QAxObject* borders = cell->querySubObject("Borders");
                for (int i = 1; i <= 4; ++i) {  // 1-4 表示上下左右边框
                    QAxObject* border = borders->querySubObject("Item(int)", i);
                    border->setProperty("LineStyle", 1);  // 1表示单实线
                }
            }
        }

        // 合并相同参考阻值的单元格
        mergeSameRefResCells(newTable_2, data["measurements"].toList());
    }
    else {
        int numRows = data["measurements"].toList().size();
        QAxObject* tables = document->querySubObject("Tables");

        int columnCount = is618A_TC ? 6 : 7;

        QAxObject* newTable = tables->querySubObject("Add(QVariant, int, int)", range->asVariant(), numRows + 1, columnCount);
        //QAxObject* newTable = tables->querySubObject("Add(QVariant, int, int)", range->asVariant(), numRows + 1, 7);

        //QStringList headers = {"通道", "参考阻值(" + data["refResUnit"].toString() + ")", "测量阻值(" + data["measResUnit"].toString() + ")", "阻值偏差(" + data["devUnit"].toString() + ")", "等效温度偏差(" + data["tempDevUnit"].toString() + ")", "允许偏差(" + data["allowedDevUnit"].toString() + ")", "校准结果"};

        QStringList headers;
        if (is618A_TC) {
            headers = QStringList({"通道", "参考电压(mV)", "测量电压(mV)", "电压偏差(mV)", "允许偏差(mV)", "校准结果"});
        } else if (is1611A_RTD || is1611A_RTD_Plus) {
            headers = QStringList({
                "通道",
                "参考阻值(Ω)",
                "测量阻值(Ω)",
                "阻值偏差(mΩ)",
                "等效温度偏差(mK)",
                "允许偏差(mK)",
                "校准结果"
            });
        } else {
            headers = QStringList({
                "通道",
                "参考阻值(" + data["refResUnit"].toString() + ")",
                "测量阻值(" + data["measResUnit"].toString() + ")",
                "阻值偏差(" + data["devUnit"].toString() + ")",
                "等效温度偏差(" + data["tempDevUnit"].toString() + ")",
                "允许偏差(" + data["allowedDevUnit"].toString() + ")",
                "校准结果"
            });
        }

        for (int i = 0; i < headers.size(); ++i) {
            QAxObject* cell = newTable->querySubObject("Cell(Row, Column)", 1, i + 1);
            QAxObject* range = cell->querySubObject("Range");
            range->setProperty("Text", headers[i]);

            QAxObject* font = range->querySubObject("Font");
            font->setProperty("Name", "宋体");
            font->setProperty("Size", 9);
            font->setProperty("Bold", true);

            QAxObject* borders = cell->querySubObject("Borders");
            for (int j = 1; j <= 4; ++j) {  // 1-4 表示上下左右边框
                QAxObject* border = borders->querySubObject("Item(int)", j);
                border->setProperty("LineStyle", 1);  // 1表示单实线
            }

            // 设置单元格内容居中
            QAxObject* paragraphFormat = range->querySubObject("ParagraphFormat");
            paragraphFormat->setProperty("Alignment", 1);  // 1表示居中对齐
        }

        int rowIndex = 2;

        for (const QVariant &measurementVariant : data["measurements"].toList()) {
            QVariantMap measurement = measurementVariant.toMap();

            for (int col = 1; col <= columnCount; ++col) {
                QAxObject* cell = newTable->querySubObject("Cell(Row, Column)", rowIndex, col);
                QAxObject* range = cell->querySubObject("Range");

                switch (col) {
                case 1: range->setProperty("Text", measurement["channel"].toString()); break;
                case 2: range->setProperty("Text", measurement["refRes"].toString()); break;
                case 3: range->setProperty("Text", measurement["measRes"].toString()); break;
                case 4: range->setProperty("Text", measurement["devRes"].toString()); break;
                case 5: {
                    if (is618A_TC) {
                        //double measValue = measurement["measRes"].toString().toDouble();
                        //double allowedDev = 0.0001 * measValue + 0.01;
                        double measValue = measurement["measRes"].toString().toDouble();
                        double allowedDev = 0.0001 * measValue + 0.01;
                        range->setProperty("Text", QString::number(allowedDev, 'f', 3)); //计算电压允许偏差值（mV）
                    } else if (is1611A_RTD || is1611A_RTD_Plus) {
                        range->setProperty("Text", calculateTempDev(measurement, data)); //电阻等效温度偏差值（mK）
                    } else {
                        range->setProperty("Text", calculateTempDev(measurement, data)); //电阻等效温度偏差值（mK）
                    }
                    break;
                }
                case 6: {
                    if (is618A_TC) {
                        double devValue = qAbs(measurement["devRes"].toString().toDouble());
                        //double measValue = measurement["measRes"].toString().toDouble();
                        //double allowedDev = 0.0001 * measValue + 0.01;
                        double measValue = measurement["measRes"].toString().toDouble();
                        double allowedDev = 0.0001 * measValue + 0.01;
                        range->setProperty("Text", devValue <= allowedDev ? "P" : "F"); //电压校准结果
                    } else {
                        QString deviceModel = data["deviceModel"].toString();
                        double allowedDev;
                        if (deviceModel.startsWith("TM14RD")) {
                            allowedDev = 50.0;
                        } else if (deviceModel.startsWith("619") && deviceModel != "619A PLUS-RTD") {
                            allowedDev = 1.0;
                        } else if (deviceModel.startsWith("H-LCW-22B")) {
                            allowedDev = 1.0;
                        } else if (is1611A_NTC) {
                            double refResValue = measurement["refRes"].toString().toDouble();
                            if (qAbs(refResValue - 1.0) < 0.5) {
                                allowedDev = 40.0; // 接近 1.0
                            } else if (qAbs(refResValue - 10.0) < 5.0) {
                                allowedDev = 20.0; // 接近 10.0
                            } else if (qAbs(refResValue - 20.0) < 10.0) {
                                allowedDev = 20.0; // 接近 20.0
                            } else {
                                allowedDev = 10.0; // 默认值
                            }
                        } else if (is1611A_NTC_Plus) {
                            double refResValue = measurement["refRes"].toString().toDouble();
                            if (qAbs(refResValue - 1.0) < 0.5) {
                                allowedDev = 15.0; // 接近 1.0
                            } else if (qAbs(refResValue - 10.0) < 5.0) {
                                allowedDev = 1.0; // 接近 10.0
                            } else if (qAbs(refResValue - 20.0) < 10.0) {
                                allowedDev = 1.0; // 接近 20.0
                            } else {
                                allowedDev = 10.0; // 默认值
                            }
                        } else if (deviceModel == "1611A-RTD") {
                            double refResValue = measurement["refRes"].toString().toDouble();
                            if (qAbs(refResValue - 100.0) < 50.0) {
                                allowedDev = 30.0; // 接近 10.0
                            } else if (qAbs(refResValue - 200.0) < 100.0) {
                                allowedDev = 40.0; // 接近 100.0
                            } else if (qAbs(refResValue - 350.0) < 175.0) {
                                allowedDev = 80.0; // 接近 350.0
                            } else if (qAbs(refResValue - 1000.0) < 500.0) {
                                allowedDev = 30.0; // 接近 1000.0
                            } else if (qAbs(refResValue - 2000.0) < 1000.0) {
                                allowedDev = 40.0; // 接近 2000.0
                            } else {
                                allowedDev = 10.0; // 默认值
                            }
                        } else if (deviceModel == "1611A-RTD Plus") {
                            double refResValue = measurement["refRes"].toString().toDouble();
                            if (qAbs(refResValue - 100.0) < 50.0) {
                                allowedDev = 10.0; // 接近 10.0
                            } else if (qAbs(refResValue - 200.0) < 100.0) {
                                allowedDev = 20.0; // 接近 100.0
                            } else if (qAbs(refResValue - 350.0) < 175.0) {
                                allowedDev = 30.0; // 接近 350.0
                            } else if (qAbs(refResValue - 1000.0) < 500.0) {
                                allowedDev = 10.0; // 接近 1000.0
                            } else if (qAbs(refResValue - 2000.0) < 1000.0) {
                                allowedDev = 20.0; // 接近 2000.0
                            } else {
                                allowedDev = 10.0; // 默认值
                            }
                        } else if (deviceModel.startsWith("618A-Plus")) {
                            allowedDev = 5.0;
                        } else if (deviceModel == "1611A-HT(PT100)" || deviceModel == "1611A-HT(PT1000)") {
                            allowedDev = 50.0;
                        } else if (deviceModel.startsWith("ZCLOG 611A")) {
                            allowedDev = 50.0;
                        } else if (deviceModel == "618A RTD") {
                            double refResValue = measurement["refRes"].toString().toDouble();
                            if (qAbs(refResValue - 100.0) < 50.0) {
                                allowedDev = 150.0; // 接近 10.0
                            } else if (qAbs(refResValue - 200.0) < 100.0) {
                                allowedDev = 270.0; // 接近 100.0
                            } else if (qAbs(refResValue - 350.0) < 175.0) {
                                allowedDev = 510.0; // 接近 350.0
                            } else {
                                allowedDev = 0.0; // 默认值
                            }
                        } else if (deviceModel == "618A PLUS-RTD") {
                            double refResValue = measurement["refRes"].toString().toDouble();
                            if (qAbs(refResValue - 100.0) < 50.0) {
                                allowedDev = 100.0; // 接近 10.0
                            } else if (qAbs(refResValue - 200.0) < 100.0) {
                                allowedDev = 180.0; // 接近 100.0
                            } else if (qAbs(refResValue - 350.0) < 175.0) {
                                allowedDev = 340.0; // 接近 350.0
                            } else {
                                allowedDev = 0.0; // 默认值
                            }
                        } else if (deviceModel == "619A PLUS-RTD") {
                            double refResValue = measurement["refRes"].toString().toDouble();
                            if (qAbs(refResValue - 100.0) < 50.0) {
                                allowedDev = 50.0; // 接近 10.0
                            } else if (qAbs(refResValue - 200.0) < 100.0) {
                                allowedDev = 90.0; // 接近 100.0
                            } else if (qAbs(refResValue - 350.0) < 175.0) {
                                allowedDev = 170.0; // 接近 350.0
                            } else {
                                allowedDev = 0.0; // 默认值
                            }
                        } else {
                            allowedDev = 10.0;
                        }
                        range->setProperty("Text", QString::number(allowedDev, 'f', 1)); //电阻允许偏差
                        // range->setProperty("Text", QString::number(data["deviceModel"].toString().startsWith("TM14RD") ? 20.0 : 10.0, 'f', 1));//电阻允许偏差
                    }
                    break;
                }
                case 7: {
                    if (!is618A_TC) {
                        double tempDev = calculateTempDev(measurement, data).toDouble();
                        QString deviceModel = data["deviceModel"].toString();
                        double allowedDev;
                        if (deviceModel.startsWith("TM14RD")) {
                            allowedDev = 50.0;
                        } else if (deviceModel.startsWith("619") && deviceModel != "619A PLUS-RTD") {
                            allowedDev = 1.0;
                        } else if (deviceModel.startsWith("H-LCW-22B")) {
                            allowedDev = 1.0;
                        }  else if (is1611A_NTC) {
                            double refResValue = measurement["refRes"].toString().toDouble();
                            if (qAbs(refResValue - 1.0) < 0.5) {
                                allowedDev = 40.0; // 接近 1.0
                            } else if (qAbs(refResValue - 10.0) < 5.0) {
                                allowedDev = 20.0; // 接近 10.0
                            } else if (qAbs(refResValue - 20.0) < 10.0) {
                                allowedDev = 20.0; // 接近 20.0
                            } else {
                                allowedDev = 10.0; // 默认值
                            }
                        } else if (is1611A_NTC_Plus) {
                            double refResValue = measurement["refRes"].toString().toDouble();
                            if (qAbs(refResValue - 1.0) < 0.5) {
                                allowedDev = 15.0; // 接近 1.0
                            } else if (qAbs(refResValue - 10.0) < 5.0) {
                                allowedDev = 1.0; // 接近 10.0
                            } else if (qAbs(refResValue - 20.0) < 10.0) {
                                allowedDev = 1.0; // 接近 20.0
                            } else {
                                allowedDev = 10.0; // 默认值
                            }
                        } else if (deviceModel == "1611A-RTD") {
                            double refResValue = measurement["refRes"].toString().toDouble();
                            if (qAbs(refResValue - 100.0) < 50.0) {
                                allowedDev = 30.0; // 接近 10.0
                            } else if (qAbs(refResValue - 200.0) < 100.0) {
                                allowedDev = 40.0; // 接近 100.0
                            } else if (qAbs(refResValue - 350.0) < 175.0) {
                                allowedDev = 80.0; // 接近 350.0
                            } else if (qAbs(refResValue - 1000.0) < 500.0) {
                                allowedDev = 30.0; // 接近 1000.0
                            } else if (qAbs(refResValue - 2000.0) < 1000.0) {
                                allowedDev = 40.0; // 接近 2000.0
                            } else {
                                allowedDev = 10.0; // 默认值
                            }
                        } else if (deviceModel == "1611A-RTD Plus") {
                            double refResValue = measurement["refRes"].toString().toDouble();
                            if (qAbs(refResValue - 100.0) < 50.0) {
                                allowedDev = 10.0; // 接近 10.0
                            } else if (qAbs(refResValue - 200.0) < 100.0) {
                                allowedDev = 20.0; // 接近 100.0
                            } else if (qAbs(refResValue - 350.0) < 175.0) {
                                allowedDev = 30.0; // 接近 350.0
                            } else if (qAbs(refResValue - 1000.0) < 500.0) {
                                allowedDev = 10.0; // 接近 1000.0
                            } else if (qAbs(refResValue - 2000.0) < 1000.0) {
                                allowedDev = 20.0; // 接近 2000.0
                            } else {
                                allowedDev = 10.0; // 默认值
                            }
                        } else if (deviceModel.startsWith("618A-Plus")) {
                            allowedDev = 5.0;
                        } else if (deviceModel == "1611A-HT(PT100)" || deviceModel == "1611A-HT(PT1000)") {
                            allowedDev = 50.0;
                        } else if (deviceModel.startsWith("ZCLOG 611A")) {
                            allowedDev = 50.0;
                        } else if (deviceModel == "618A RTD") {
                            double refResValue = measurement["refRes"].toString().toDouble();
                            if (qAbs(refResValue - 100.0) < 50.0) {
                                allowedDev = 150.0; // 接近 10.0
                            } else if (qAbs(refResValue - 200.0) < 100.0) {
                                allowedDev = 270.0; // 接近 100.0
                            } else if (qAbs(refResValue - 350.0) < 175.0) {
                                allowedDev = 510.0; // 接近 350.0
                            } else {
                                allowedDev = 0.0; // 默认值
                            }
                        } else if (deviceModel == "618A PLUS-RTD") {
                            double refResValue = measurement["refRes"].toString().toDouble();
                            if (qAbs(refResValue - 100.0) < 50.0) {
                                allowedDev = 100.0; // 接近 10.0
                            } else if (qAbs(refResValue - 200.0) < 100.0) {
                                allowedDev = 180.0; // 接近 100.0
                            } else if (qAbs(refResValue - 350.0) < 175.0) {
                                allowedDev = 340.0; // 接近 350.0
                            } else {
                                allowedDev = 0.0; // 默认值
                            }
                        } else if (deviceModel == "619A PLUS-RTD") {
                            double refResValue = measurement["refRes"].toString().toDouble();
                            if (qAbs(refResValue - 100.0) < 50.0) {
                                allowedDev = 50.0; // 接近 10.0
                            } else if (qAbs(refResValue - 200.0) < 100.0) {
                                allowedDev = 90.0; // 接近 100.0
                            } else if (qAbs(refResValue - 350.0) < 175.0) {
                                allowedDev = 170.0; // 接近 350.0
                            } else {
                                allowedDev = 0.0; // 默认值
                            }
                        } else {
                            allowedDev = 10.0;
                        }
                        //double allowedDev = data["deviceModel"].toString().startsWith("TM14RD") ? 20.0 : 10.0;
                        range->setProperty("Text", qAbs(tempDev) <= allowedDev ? "P" : "F");
                    }
                    break;
                }
                }

                // 设置单元格样式
                setCellStyle(cell);
            }
            rowIndex++;
        }


        // 设置除表头外所有单元格的边框和字体
        for (int row = 2; row <= rowIndex; ++row) {
            for (int col = 1; col <= columnCount; ++col) {
                QAxObject* cell = newTable->querySubObject("Cell(Row, Column)", row, col);
                QAxObject* range = cell->querySubObject("Range");

                QAxObject* font = range->querySubObject("Font");
                font->setProperty("Name", "宋体");
                font->setProperty("Size", 9);
                font->setProperty("Bold", false);

                QAxObject* paragraphFormat = range->querySubObject("ParagraphFormat");
                paragraphFormat->setProperty("Alignment", 1);  // 1表示居中对齐

                QAxObject* borders = cell->querySubObject("Borders");
                for (int i = 1; i <= 4; ++i) {  // 1-4 表示上下左右边框
                    QAxObject* border = borders->querySubObject("Item(int)", i);
                    border->setProperty("LineStyle", 1);  // 1表示单实线
                }
            }
        }

        // 合并相同参考阻值的单元格
        mergeSameRefResCells(newTable, data["measurements"].toList());
    }


    // 临时保存文件到应用程序的临时目录
    QString lineEdit1 = data["lineEdit1"].toString();
    QString currentTimestamp = QDateTime::currentDateTime().toString("yyyyMMddHHmmss");

    // 创建新的 timestamp
    QString timestamp = QString("%1-%2").arg(lineEdit1, currentTimestamp);

    // 创建临时路径
    // 获取应用程序目录
    QString appDir = QCoreApplication::applicationDirPath();

    // 创建 TempDoc 文件夹
    QDir dir(appDir);
    if (!dir.exists("TempDoc")) {
        dir.mkdir("TempDoc");
    }

    // 创建临时路径
    QString tempPath = appDir + "/TempDoc/TempDoc_" + timestamp + ".pdf";
    QString tempPath_Word = appDir + "/TempDoc/TempDoc_" + timestamp + ".docx";

    // 系统temp路径
    //QString tempPath = QDir::tempPath() + "/TempDoc_" + timestamp + ".docx";
    //QString tempPath = QDir::tempPath() + "/TempDoc_" + timestamp + ".pdf";

    document->dynamicCall("SaveAs(const QString&)", QDir::toNativeSeparators(tempPath_Word)); //Word
    document->dynamicCall("SaveAs(const QString&, int)", QDir::toNativeSeparators(tempPath), 17); //PDF
    document->dynamicCall("Close (boolean)", false);
    word->dynamicCall("Quit()");

    emit finished(timestamp, tempPath);

    CoUninitialize();
}

void ExportTask::MergeCells(QAxObject *table, int nStartRow, int nStartCol, int nEndRow, int nEndCol) {
    if (nStartRow == nEndRow && nStartCol == nEndCol) {
        // 如果开始和结束单元格相同，则不执行合并操作
        return;
    }

    QAxObject* StartCell = table->querySubObject("Cell(int, int)", nStartRow, nStartCol);
    QAxObject* EndCell = table->querySubObject("Cell(int, int)", nEndRow, nEndCol);
    StartCell->dynamicCall("Merge(LPDISPATCH)", EndCell->asVariant());

    // After merging, set the alignment of the merged cell to center
    QAxObject* range = StartCell->querySubObject("Range");
    QAxObject* paragraphFormat = range->querySubObject("ParagraphFormat");
    paragraphFormat->setProperty("Alignment", 1); // 1 represents wdAlignParagraphCenter

    // 设置垂直居中
    QAxObject* cells = range->querySubObject("Cells");
    cells->setProperty("VerticalAlignment", 1);

    // Set font style for the merged cell
    QAxObject* font = range->querySubObject("Font");
    font->setProperty("Name", "宋体");
    font->setProperty("Size", 9);
    font->setProperty("Bold", false);

//    QAxObject* borders = StartCell->querySubObject("Borders");
//    for (int j = 1; j <= 4; ++j) {  // 1-4 表示上下左右边框
//        QAxObject* border = borders->querySubObject("Item(int)", j);
//        border->setProperty("LineStyle", 1);  // 1表示单实线
//    }

    // Clean up
    delete font;
    delete paragraphFormat;
    delete range;
    delete StartCell;
    delete EndCell;
}

void ExportTask::mergeSameRefResCells(QAxObject* table, const QVariantList& measurements) {
    QString prevRefRes;
    int startRow = 2;
    int currentRow = 2;

    foreach (const QVariant& measurementVariant, measurements) {
        QVariantMap measurement = measurementVariant.toMap();
        QString currentRefRes = measurement["refRes"].toString();

        if (currentRefRes == prevRefRes) {
            // 如果参考阻值相同，则继续，但先清空当前行第二列的值
            QAxObject* currentCell = table->querySubObject("Cell(int, int)", currentRow, 2);
            QAxObject* currentRange = currentCell->querySubObject("Range");
            currentRange->setProperty("Text", "");
            delete currentRange;
            delete currentCell;

            currentRow++;
        } else {
            // 如果参考阻值不同，合并前一组单元格（如果有多于一行）
            if (startRow < currentRow) {
                MergeCells(table, startRow, 2, currentRow - 1, 2);           
            }
            startRow = currentRow;
            currentRow++;
        }
        prevRefRes = currentRefRes;
    }

    // Merge the last group of cells if necessary
    if (startRow < currentRow) {
        MergeCells(table, startRow, 2, currentRow - 1, 2);
    }
}

void ExportTask::mergeSameRefResCellsAny(QAxObject* table, const QVariantList& measurements, int column, const QString& fieldName) {
    // 获取表格的列数
    QAxObject* columns = table->querySubObject("Columns");
    int columnCount = columns->property("Count").toInt();
    delete columns;

    // 如果传入的列索引为负数，则转换为正数索引
    if (column < 0) {
        column = columnCount + column + 1;
    }

    QString prevValue;
    int startRow = 2;
    int currentRow = 2;

    foreach (const QVariant& measurementVariant, measurements) {
        QVariantMap measurement = measurementVariant.toMap();
        QString currentValue = measurement[fieldName].toString();

        if (currentValue == prevValue) {
            // 如果字段值相同，则继续，但先清空当前行指定列的值
            QAxObject* currentCell = table->querySubObject("Cell(int, int)", currentRow, column);
            QAxObject* currentRange = currentCell->querySubObject("Range");
            currentRange->setProperty("Text", "");
            delete currentRange;
            delete currentCell;

            currentRow++;
        } else {
            // 如果字段值不同，合并前一组单元格（如果有多于一行）
            if (startRow < currentRow) {
                MergeCells(table, startRow, column, currentRow - 1, column);
            }
            startRow = currentRow;
            currentRow++;
        }
        prevValue = currentValue;
    }

    // Merge the last group of cells if necessary
    if (startRow < currentRow) {
        MergeCells(table, startRow, column, currentRow - 1, column);
    }
}

void ExportTask::setParagraphFormat(QAxObject* range) {
    QAxObject* paragraph = range->querySubObject("Paragraphs(1)");
    QAxObject* paragraphFormat = paragraph->querySubObject("Range()")->querySubObject("ParagraphFormat");
    paragraphFormat->setProperty("SpaceBefore", 0); // 段前间距
    paragraphFormat->setProperty("SpaceAfter", 0);  // 段后间距
    paragraphFormat->setProperty("LineSpacingRule", 4); // 4表示固定行距
    paragraphFormat->setProperty("LineSpacing", 9); // 0.75倍行距（12磅的0.75倍）
}


void ExportTask::replaceBookmarkText(QAxObject* document, const QString &bookmarkName, const QString &text) {
    QAxObject *bookmark = document->querySubObject("Bookmarks(QVariant)", bookmarkName);
    if (!bookmark->isNull()) {
        bookmark->dynamicCall("Select(void)");
        QAxObject *range = bookmark->querySubObject("Range");
        range->setProperty("Text", text);
        // 设置字体样式
        QAxObject *font = range->querySubObject("Font");
        font->setProperty("Name", "宋体"); // 字体名称
        font->setProperty("Size", 9); // 字体大小
        font->setProperty("Bold", false); // 非粗体
        font->setProperty("Italic", false); // 非斜体
        font->setProperty("Underline", false); // 无下划线
        font->setProperty("Color", 0); // 字体颜色为黑色
        delete font;
        delete range;
    }
    delete bookmark;
}

void ExportTask::addRowToTable(QAxObject* rows, const QVariantMap& rowData) {
    QAxObject* row = rows->querySubObject("Add()");
    if (row) {
        QAxObject* cell1 = row->querySubObject("Cells(int)", 1);
        QAxObject* cell2 = row->querySubObject("Cells(int)", 2);
        QAxObject* cell3 = row->querySubObject("Cells(int)", 3);
        QAxObject* cell4 = row->querySubObject("Cells(int)", 4);

        if (cell1 && cell2 && cell3 && cell4) {
            cell1->querySubObject("Range")->setProperty("Text", rowData["checkBoxText"].toString());
            cell2->querySubObject("Range")->setProperty("Text", rowData["deviceModelText"].toString());
            cell3->querySubObject("Range")->setProperty("Text", rowData["serialNumText"].toString());
            cell4->querySubObject("Range")->setProperty("Text", rowData["dateTimeText"].toString());

            cell1->querySubObject("Range")->setProperty("Style", "校准设备表");
            cell2->querySubObject("Range")->setProperty("Style", "校准设备表");
            cell3->querySubObject("Range")->setProperty("Style", "校准设备表");
            cell4->querySubObject("Range")->setProperty("Style", "校准设备表");

            delete cell1;
            delete cell2;
            delete cell3;
            delete cell4;
        }

        delete row;
    }
}

// 计算等效温度偏差
QString ExportTask::calculateTempDev(const QVariantMap& measurement, const QVariantMap& data) { //旧版：仅适用于参考阻值 Ω
    bool isKOhm = data["isKOhm"].toBool();
    QString deviceModel = data["deviceModel"].toString(); // 获取 deviceModel 参数

    double refResValue = measurement["refRes"].toString().toDouble(); //参考阻值 Ω
    double devResValue;

    bool is1611A_RTD = data["is1611A_RTD"].toBool();
    bool is1611A_RTD_Plus = data["is1611A_RTD_Plus"].toBool();

    if (is1611A_RTD || is1611A_RTD_Plus) {
        devResValue = measurement["devRes"].toString().toDouble() / 1000.0;// 1611A-RTD--mΩ 转为Ω
    } else {
        devResValue = measurement["devRes"].toString().toDouble(); //阻值偏差 Ω
    }

    double S = calculateS(refResValue, isKOhm, deviceModel);
    double tempDev = S != 0 ? devResValue / S : 0.0;

    double adjustedTempDev = tempDev * 10.0 + (tempDev > 0 ? 0.0000001 : -0.0000001);
    double roundedTempDev = std::round(adjustedTempDev) / 10.0;
    return QString::number(roundedTempDev, 'f', 1);
}

double ExportTask::calculateS(double refResValue, bool isKOhm, const QString& deviceModel) { //根据参考电阻值静态匹配S值，而非型号匹配
//    const double epsilon = 0.01; // 允许的输入挡位值误差范围，可以根据需要调整

//    auto isClose = [epsilon](double a, double b) {
//        return std::abs(a - b) < epsilon;
//    };

    const double epsilonKOhm = 0.01; // 允许的输入挡位值误差范围，可以根据需要调整
    const double epsilonOhm = 5.0; // 非kOhm情况下的误差范围

    auto isClose = [epsilonKOhm, epsilonOhm, isKOhm](double a, double b) {
        return std::abs(a - b) < (isKOhm ? epsilonKOhm : epsilonOhm);
    };

    if (deviceModel == "1611A-HT(PT100)") {
        //qDebug() << "计算1611A-HT的S值...";
        if (isKOhm) { // 没用到
            return 0.000385;
        } else {
            if (isClose(refResValue, 10.0)) return 0.000385;
            if (isClose(refResValue, 100.0)) return 0.000385;
            if (isClose(refResValue, 350.0)) return 0.000385;
        }
    }else if (deviceModel == "1611A-HT(PT1000)") {
        if (isClose(refResValue, 350.0)) return 0.00385;
        if (isClose(refResValue, 1000.0)) return 0.00385;
        if (isClose(refResValue, 2000.0)) return 0.00385;
    } else if (deviceModel == "1611A-RTD") {
        if (isClose(refResValue, 100.0)) return 0.000385;
        if (isClose(refResValue, 200.0)) return 0.000385;
        if (isClose(refResValue, 350.0)) return 0.000385;
        if (isClose(refResValue, 1000.0)) return 0.00385;
        if (isClose(refResValue, 2000.0)) return 0.00385;
    } else if (deviceModel == "1611A-RTD Plus") {
        if (isClose(refResValue, 100.0)) return 0.000385;
        if (isClose(refResValue, 200.0)) return 0.000385;
        if (isClose(refResValue, 350.0)) return 0.000385;
        if (isClose(refResValue, 1000.0)) return 0.00385;
        if (isClose(refResValue, 2000.0)) return 0.00385;
    } else if(deviceModel == "TM14RD-PT100"){
        return 0.000385;
    } else if(deviceModel == "TM14RD-PT1000"){
        return 0.00385;
    } else if (deviceModel == "ZCLOG 611A") {
        if (isClose(refResValue, 100.0)) return 0.000385;
        if (isClose(refResValue, 200.0)) return 0.000385;
        if (isClose(refResValue, 350.0)) return 0.000385;
        if (isClose(refResValue, 1000.0)) return 0.00385;
        if (isClose(refResValue, 2000.0)) return 0.00385;
    } else if (deviceModel == "618A RTD" || deviceModel == "618A PLUS-RTD" || deviceModel == "619A PLUS-RTD") {
        if (isClose(refResValue, 100.0)) return 0.000385;
        if (isClose(refResValue, 200.0)) return 0.000385;
        if (isClose(refResValue, 350.0)) return 0.000385;
        if (isClose(refResValue, 1000.0)) return 0.00385;
        if (isClose(refResValue, 2000.0)) return 0.00385;
    } else {
        if (isKOhm) {
            if (isClose(refResValue, 0.1)) return 0.0077;
            if (isClose(refResValue, 0.2)) return 0.01155;
            if (isClose(refResValue, 1.0)) return 0.026;
            if (isClose(refResValue, 5.0)) return 0.17;
            if (isClose(refResValue, 10.0)) return 0.4;
            if (isClose(refResValue, 20.0)) return 0.9;
        } else {
            if (isClose(refResValue, 100.0)) return 0.0077;
            if (isClose(refResValue, 200.0)) return 0.01155;
            if (isClose(refResValue, 1000.0)) return 0.026;
            if (isClose(refResValue, 5000.0)) return 0.17;
            if (isClose(refResValue, 10000.0)) return 0.4;
            if (isClose(refResValue, 20000.0)) return 0.9;
        }
    }
    return 0.0;

}

void ExportTask::setCellStyle(QAxObject* cell) {
    QAxObject* range = cell->querySubObject("Range");
    QAxObject* font = range->querySubObject("Font");
    font->setProperty("Name", "宋体");
    font->setProperty("Size", 9);
    font->setProperty("Bold", false);

    QAxObject* paragraphFormat = range->querySubObject("ParagraphFormat");
    paragraphFormat->setProperty("Alignment", 1);  // 1表示居中对齐

    QAxObject* borders = cell->querySubObject("Borders");
    for (int i = 1; i <= 4; ++i) {
        QAxObject* border = borders->querySubObject("Item(int)", i);
        border->setProperty("LineStyle", 1);  // 1表示单实线
    }

    delete font;
    delete paragraphFormat;
    delete borders;
    delete range;
}

double ExportTask::calculateAllowedDev(const QString& deviceModel, double refResValue) { //传入的refResValue单位要求为k
    if (deviceModel.startsWith("TM14RD")) {
        return 50.0;
    } else if (deviceModel.startsWith("619") && deviceModel != "619A PLUS-RTD") {
        return 1.0;
    } else if (deviceModel.startsWith("H-LCW-22B")) {
        return 1.0;
    } else if (deviceModel == "1611A-NTC") {
        if (qAbs(refResValue - 1.0) < 0.5) {
            return 40.0; // 接近 1.0
        } else if (qAbs(refResValue - 10.0) < 5.0) {
            return 20.0; // 接近 10.0
        } else if (qAbs(refResValue - 20.0) < 10.0) {
            return 20.0; // 接近 20.0
        } else {
            return 10.0; // 默认值
        }
    } else if (deviceModel == "1611A-NTC Plus") {
        if (qAbs(refResValue - 1.0) < 0.5) {
            return 15.0; // 接近 1.0
        } else if (qAbs(refResValue - 10.0) < 5.0) {
            return 1.0; // 接近 10.0
        } else if (qAbs(refResValue - 20.0) < 10.0) {
            return 1.0; // 接近 20.0
        } else {
            return 10.0; // 默认值
        }
    } else if (deviceModel.startsWith("618A-Plus")) {
        return 5.0;
    } else if (deviceModel == "618A-TC-DB") {
        return 200.0;
    } else if (deviceModel.startsWith("1611A-TC") || deviceModel.startsWith("1611A-VS")) {
        return 200.0;
    } else if (deviceModel.startsWith("TM22XND-P")) { // Ω 保留两位允许偏差
        if (qAbs(refResValue - 1.0) < 0.5) {
            return 0.03; // 接近 1.0
        } else if (qAbs(refResValue - 10.0) < 5.0) {
            return 0.25; // 接近 10.0
        } else if (qAbs(refResValue - 20.0) < 10.0) {
            return 0.50; // 接近 20.0
        } else {
            return 0.00; // 默认值
        }
    } else if (deviceModel.startsWith("TM18ND")) { // Ω 保留两位允许偏差
        if (qAbs(refResValue - 1.0) < 0.5) {
            return 0.2; // 接近 1.0
        } else if (qAbs(refResValue - 10.0) < 5.0) {
            return 0.3; // 接近 10.0
        } else if (qAbs(refResValue - 20.0) < 10.0) {
            return 1.6; // 接近 20.0
        } else {
            return 0.0; // 默认值
        }
    } else if (deviceModel.startsWith("TM18RD-P")) { // Ω 保留两位允许偏差
        if (qAbs(refResValue - 350.0) < 175.0) {
            return 0.04; // 接近 1.0
        } else if (qAbs(refResValue - 1000.0) < 500.0) {
            return 0.05; // 接近 10.0
        } else if (qAbs(refResValue - 2000.0) < 1000.0) {
            return 0.10; // 接近 20.0
        } else {
            return 0.0; // 默认值
        }
    } else if (deviceModel.startsWith("TM18MD-P-T1")) { // Ω 保留两位允许偏差
        if (qAbs(refResValue - 0.35) < 0.175) {
            return 0.04; // 接近 0.35
        } else if (qAbs(refResValue - 1.0) < 0.5) {
            return 0.05; // 接近 1.0
        } else if (qAbs(refResValue - 2.0) < 1.0) {
            return 0.10; // 接近 2.0
        } else {
            return 0.0; // 默认值
        }
    } else if (deviceModel.startsWith("TM18MD-P-T2-8")) { // Ω 保留两位允许偏差
        if (qAbs(refResValue - 1.0) < 0.5) {
            return 0.2; // 接近 1.0
        } else if (qAbs(refResValue - 10.0) < 5.0) {
            return 0.3; // 接近 10.0
        } else if (qAbs(refResValue - 20.0) < 10.0) {
            return 1.6; // 接近 20.0
        } else {
            return 0.0; // 默认值
        }
    } else {
        return 10.0;
    }
}

QAxObject* ExportTask::createTestTable(QAxObject* document, QAxObject* range,
                                       const QStringList& headers, const QVariantList& combinedResults,
                                       int repeatTestCHCount) {
    // 获取1Hz和10Hz的数据
    QVariantList results1Hz = combinedResults[0].toList();
    QVariantList results10Hz = combinedResults[1].toList();

    // 创建1Hz表格
    // 添加1Hz标题段落
    QAxObject* paragraphs = document->querySubObject("Paragraphs");
    QAxObject* newParagraph = paragraphs->querySubObject("Add(QVariant)", range->asVariant());
    range = newParagraph->querySubObject("Range");
    range->setProperty("Text", "10分钟重复性测试@1Hz：");

    // 设置1Hz标题字体
    QAxObject* font = range->querySubObject("Font");
    if (font) {
        font->setProperty("Name", "宋体");
        font->setProperty("Size", 10);
        font->setProperty("Bold", false);
        delete font;
    }

    // 设置1Hz标题格式
    QAxObject* titleFormat = range->querySubObject("ParagraphFormat");
    if (titleFormat) {
        titleFormat->setProperty("Alignment", 0); // 左对齐
        titleFormat->setProperty("LineSpacingRule", 0);
        titleFormat->setProperty("SpaceBefore", 6);
        delete titleFormat;
    }

    range->dynamicCall("Collapse(int)", 0);

    // 创建1Hz表格
    int rowCount = results1Hz.size() + 1;
    int columnCount = headers.size();

    QAxObject* tables = document->querySubObject("Tables");
    QAxObject* table1Hz = tables->querySubObject("Add(QVariant, int, int)",
                                                 range->asVariant(), rowCount, columnCount);

    // 设置1Hz表格默认段落格式
    QAxObject* tableRange = table1Hz->querySubObject("Range");
    if (tableRange) {
        QAxObject* paraFormat = tableRange->querySubObject("ParagraphFormat");
        if (paraFormat) {
            paraFormat->setProperty("SpaceBefore", 0);
            paraFormat->setProperty("SpaceAfter", 0);
            paraFormat->setProperty("LineSpacingRule", 0);
            delete paraFormat;
        }
        delete tableRange;
    }

    // 设置1Hz表格属性
    table1Hz->dynamicCall("AutoFitBehavior(WdAutoFitBehavior)", 2);
    table1Hz->setProperty("VerticalAlignment", 1);
    table1Hz->setProperty("Alignment", 1);

    // 填充1Hz表格数据
    fillTableData(table1Hz, headers, results1Hz, repeatTestCHCount);

    // 合并相同的参考电阻单元格
    mergeSameRefResCellsAny(table1Hz, results1Hz, -2, "techRequirement");

    range = table1Hz->querySubObject("Range");
    range->dynamicCall("Collapse(int)", 0);

    // 创建10Hz表格
    // 添加10Hz标题段落
    newParagraph = paragraphs->querySubObject("Add(QVariant)", range->asVariant());
    range = newParagraph->querySubObject("Range");
    range->setProperty("Text", "10分钟重复性测试@10Hz：");

    // 设置10Hz标题字体为宋体，10号，常规
    font = range->querySubObject("Font");
    if (font) {
        font->setProperty("Name", "宋体");
        font->setProperty("Size", 10);
        font->setProperty("Bold", false);
        delete font;
    }

    // 设置10Hz标题格式
    titleFormat = range->querySubObject("ParagraphFormat");
    if (titleFormat) {
        titleFormat->setProperty("Alignment", 0); // 左对齐
        titleFormat->setProperty("LineSpacingRule", 0);
        titleFormat->setProperty("SpaceBefore", 6);
        delete titleFormat;
    }

    range->dynamicCall("Collapse(int)", 0);

    // 创建10Hz表格
    rowCount = results10Hz.size() + 1;

    QAxObject* table10Hz = tables->querySubObject("Add(QVariant, int, int)",
                                                  range->asVariant(), rowCount, columnCount);

    // 设置10Hz表格默认段落格式
    tableRange = table10Hz->querySubObject("Range");
    if (tableRange) {
        QAxObject* paraFormat = tableRange->querySubObject("ParagraphFormat");
        if (paraFormat) {
            paraFormat->setProperty("SpaceBefore", 0);
            paraFormat->setProperty("SpaceAfter", 0);
            paraFormat->setProperty("LineSpacingRule", 0);
            delete paraFormat;
        }
        delete tableRange;
    }

    // 设置10Hz表格属性
    table10Hz->dynamicCall("AutoFitBehavior(WdAutoFitBehavior)", 2);
    table10Hz->setProperty("VerticalAlignment", 1);
    table10Hz->setProperty("Alignment", 1);

    // 填充10Hz表格数据
    fillTableData(table10Hz, headers, results10Hz, repeatTestCHCount);

    // 合并相同的参考电阻单元格
    mergeSameRefResCellsAny(table10Hz, results10Hz, -2, "techRequirement");

    range = table10Hz->querySubObject("Range");
    range->dynamicCall("Collapse(int)", 0);

    return range;
}
// 辅助函数：填充表格数据
void ExportTask::fillTableData(QAxObject* table, const QStringList& headers,
                               const QVariantList& results, int repeatTestCHCount) {
    int columnCount = headers.size();

    // 设置表头
    for (int col = 1; col <= columnCount; ++col) {
        QAxObject* cell = table->querySubObject("Cell(Row, Column)", 1, col);
        if (!cell) continue;

        QAxObject* cellRange = cell->querySubObject("Range");
        cellRange->setProperty("Text", headers[col-1]);

        // 设置表头样式
        QAxObject* font = cellRange->querySubObject("Font");
        if (font) {
            font->setProperty("Name", "宋体");
            font->setProperty("Size", 9);
            font->setProperty("Bold", true);
            delete font;
        }
        // 设置边框和对齐
        setTableCellFormat(cell, cellRange);
    }

    // 填充数据行
    int rowIndex = 2;
    for (const QVariant& resultVar : results) {
        QVariantMap result = resultVar.toMap();
        QVariantList stdDevs = result["stdDevValues"].toList();

        for (int col = 1; col <= columnCount; ++col) {
            QAxObject* cell = table->querySubObject("Cell(int, int)", rowIndex, col);
            if (!cell) continue;

            QAxObject* cellRange = cell->querySubObject("Range");

            // 设置单元格内容
            if (col == 1) {
                cellRange->setProperty("Text", QString("%1 %2")
                                                   .arg(result["resistanceValue"].toString())
                                                   .arg(result["resistanceUnit"].toString()));
            }
            else if (col >= 2 && col < 2 + repeatTestCHCount) {
                int channelIndex = col - 2;
                if (channelIndex < stdDevs.size()) {
                    cellRange->setProperty("Text",
                                           QString::number(stdDevs[channelIndex].toDouble(), 'f', 1));
                }
            }
            else if (col == columnCount - 1) {
                cellRange->setProperty("Text",
                                       QString::number(result["techRequirement"].toDouble(), 'f', 1));
            }
            else if (col == columnCount) {
                QString conclusion = result["isQualified"].toBool() ? "合格" : "不合格";
                cellRange->setProperty("Text", conclusion);
            }

            // 设置单元格格式
            setTableCellFormat(cell, cellRange);
        }
        rowIndex++;
    }
}

// 辅助函数：设置单元格格式
void ExportTask::setTableCellFormat(QAxObject* cell, QAxObject* cellRange) {
    // 设置字体
    QAxObject* font = cellRange->querySubObject("Font");
    if (font) {
        font->setProperty("Name", "宋体");
        font->setProperty("Size", 9);
        delete font;
    }

    // 设置对齐
    QAxObject* paraFormat = cellRange->querySubObject("ParagraphFormat");
    if (paraFormat) {
        paraFormat->setProperty("Alignment", 1);
        paraFormat->setProperty("SpaceBefore", 0);
        paraFormat->setProperty("SpaceAfter", 0);
        paraFormat->setProperty("LineSpacingRule", 0);
        delete paraFormat;
    }

    // 设置边框
    QAxObject* borders = cell->querySubObject("Borders");
    if (borders) {
        for (int i = 1; i <= 4; ++i) {
            QAxObject* border = borders->querySubObject("Item(int)", i);
            if (border) {
                border->setProperty("LineStyle", 1);
                delete border;
            }
        }
        delete borders;
    }
}

