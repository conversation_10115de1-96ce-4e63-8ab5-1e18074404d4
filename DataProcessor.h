﻿#ifndef DATAPROCESSOR_H
#define DATAPROCESSOR_H

#include <QObject>
#include <QString>
#include <QByteArray>

// 数据处理器抽象接口
class DataProcessor : public QObject {
    Q_OBJECT
public:
    explicit DataProcessor(QObject* parent = nullptr) : QObject(parent) {}
    virtual bool supportsCommand(const QString& command) const = 0;  // 根据命令前缀判断是否包含对应的processor
    virtual void processData(const QByteArray& data, const QString& command) = 0;

signals:
    void resultReady(bool status, const QString& result, const QString& command);
};

#endif // DATAPROCESSOR_H
