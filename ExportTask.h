﻿#ifndef EXPORTTASK_H
#define EXPORTTASK_H

#include <QObject>
#include <QAxObject>
#include <QVariantMap>
#include <QStringList>
#include <QDateTime>
#include <objbase.h>
#include <QDebug>
#include <QRunnable>
#include <QTemporaryFile>
#include "VerificationWorker.h"

//class ExportTask : public QObject {
//    Q_OBJECT
//public:
//    explicit ExportTask(const QVariantMap& data);

// 定义设备信息结构
struct SensorInfo {
    QString sensorTypeName;     // 传感器类型名称
    QString resistance;         // 标称电阻
};

class ExportTask : public QObject, public QRunnable {
    Q_OBJECT
public:
    explicit ExportTask(const QString& savePath, const ProjectData& projectData); // 声明 cpp中实现

    void run() override {
        process();
    }

public slots:
    void process();
    void slots_exception(int code, const QString &source, const QString &desc, const QString &help);

signals:
    //void finished(const QString& fileName, const QString& filePath);
    void finished(bool success, const QString& filePath);
    void progress(int value);

private:
    QString savePath;
    ProjectData projectData;

    std::unique_ptr<QTemporaryFile> m_tempFile;
    QString m_templatePath;
    QString createTemporaryFileFromResource(const QString& resourcePath);



    QVariantMap data;
    void replaceBookmarkText(QAxObject* document, const QString &bookmarkName, const QString &text);
    void addRowToTable(QAxObject* rows, const QVariantMap& rowData);
    void MergeCells(QAxObject *table, int nStartRow,int nStartCol,int nEndRow,int nEndCol);//合并单元格
    // void mergeSameRefResCells(QAxObject* table, const QVariantList& measurements);
    void mergeReferenceValueCells(QAxObject* table, const QVector<ReferenceData>& referenceValues);
    void mergeSameRefResCellsAny(QAxObject* table, const QVariantList& measurements, int column, const QString& fieldName); //合并任意列单元格
    void MergeCellsRandom(QAxObject *table, int nStartRow, int nStartCol, int nEndRow, int nEndCol);
    void setCellStyle(QAxObject* table, int row, int col);


    QString calculateTempDev(const QVariantMap& measurement, const QVariantMap& data);
    double calculateS(double refResValue, bool isKOhm, const QString& deviceModel);
    void setCellStyle(QAxObject* cell);

    double calculateAllowedDev(const QString& deviceModel, double refResValue);

    void setParagraphFormat(QAxObject* range); //设置空行间距

    QAxObject* createTestTable(QAxObject* document, QAxObject* range,
                               const QStringList& headers, const QVariantList& combinedResults,
                               int repeatTestCHCount);
    void fillTableData(QAxObject* table, const QStringList& headers,
                       const QVariantList& results, int repeatTestCHCount);
    void setTableCellFormat(QAxObject* cell, QAxObject* cellRange);


    QVariantList generateMeasurements(const ProjectData& projectData);

    /*******************************************/
    //    61XA：测温模块
    //    TMXXX: 温度采集器 TM14XXX 温度变送器
    //    H-LCW-22B: 测温及控制模块
    //    1611A: 精密测温仪
    //    ZCLOG 334NTC：测温模块
    /*******************************************/
    QString calMethod_61X = "测温模块的温度测量（热敏电阻温度测量）功能，通过测量参考电阻值偏差，等效换算为热敏电阻温度传感器（标称电阻为10kΩ）温度偏差进行校准。\n"
                            "校准数据包含参考阻值、测量阻值、阻值偏差、等效温度偏差、允许偏差、校准结果。"
                            "参考阻值是使用校准设备测量的参考电阻阻值，测量阻值是使用测温模块测量的参考电阻阻值，"
                            "阻值偏差是校准设备测量值与测温模块测量值的差值，等效温度偏差是将阻值偏差根据热敏电阻阻值-温度表换算得出，"
                            "允许偏差是测温模块给定的温度准确度，校准结果评定为通过(P)、不通过(F)。";

    QString calMethod_61X_RTD = "测温模块的温度测量（铂电阻温度测量）功能，通过测量参考电阻值偏差，等效换算为铂电阻温度传感器（标称电阻为100Ω）温度偏差进行校准。\n"
                                "校准数据包含参考阻值、测量阻值、阻值偏差、等效温度偏差、允许偏差、校准结果。"
                                "参考阻值是使用校准设备测量的参考电阻阻值，测量阻值是使用测温模块测量的参考电阻阻值，"
                                "阻值偏差是校准设备测量值与测温模块测量值的差值，等效温度偏差是将阻值偏差根据铂电阻阻值-温度表换算得出，"
                                "允许偏差是测温模块给定的温度准确度，校准结果评定为通过(P)、不通过(F)。";


    QString calMethod_TM14RD = "温度采集器的温度测量（铂电阻温度测量）功能，通过测量参考电阻值偏差，等效换算为铂电阻温度传感器（标称电阻为100Ω）温度偏差进行校准。\n"
                               "校准数据包含参考阻值、测量阻值、阻值偏差、等效温度偏差、允许偏差、校准结果。"
                               "参考阻值是使用校准设备测量的参考电阻阻值，测量阻值是使用温度采集器测量的参考电阻阻值，"
                               "阻值偏差是校准设备测量值与温度采集器测量值的差值，等效温度偏差是将阻值偏差根据铂电阻阻值-温度表换算得出，"
                               "允许偏差是温度采集器给定的温度准确度，校准结果评定为通过(P)、不通过(F)。";

    QString calMethod_TM14ND = "温度变送器的温度测量（热敏电阻温度测量）功能，通过测量参考电阻值偏差，等效换算为热敏电阻温度传感器（标称电阻为10kΩ）温度偏差进行校准。\n"
                               "校准数据包含参考阻值、测量阻值、阻值偏差、等效温度偏差、允许偏差、校准结果。"
                               "参考阻值是使用校准设备测量的参考电阻阻值，测量阻值是使用温度变送器测量的参考电阻阻值，"
                               "阻值偏差是校准设备测量值与温度变送器测量值的差值，等效温度偏差是将阻值偏差根据热敏电阻阻值-温度表换算得出，"
                               "允许偏差是温度变送器给定的温度准确度，校准结果评定为通过(P)、不通过(F)。";
};

#endif // EXPORTTASK_H
