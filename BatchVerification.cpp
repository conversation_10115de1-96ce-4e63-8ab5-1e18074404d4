#include "BatchVerification.h"
#include <QThread>
#include <QCoreApplication>
#include <QMessageBox>
#include <QApplication>

BatchVerificationManager::BatchVerificationManager(QObject *parent)
    : QObject(parent), m_worker(nullptr), m_currentBatchIndex(0),
      m_currentReferenceIndex(0), m_abortRequested(false)
{
}

BatchVerificationManager::~BatchVerificationManager()
{
    cleanupWorker();
}

void BatchVerificationManager::setCommandHandlers(CommandHandlerFunc calHandler, CommandHandlerFunc deviceHandler)
{
    m_calHandler = calHandler;
    m_deviceHandler = deviceHandler;
}

void BatchVerificationManager::cleanupWorker()
{
    if (m_worker)
    {
        m_worker = nullptr;
    }
}

void BatchVerificationManager::startBatchVerification(const AdjDeviceConfig &deviceConfig,
                                                      CommandHandlerFunc calHandler,
                                                      CommandHandlerFunc deviceHandler)
{
    m_deviceConfig = deviceConfig;
    m_calHandler = calHandler;
    m_deviceHandler = deviceHandler;
    m_abortRequested = false;
    m_currentBatchIndex = 0;     // 当前批次索引（优化后：外层循环）
    m_currentReferenceIndex = 0; // 不再使用，保留以兼容现有代码

    // 1220 has 20 channels; reserve channels for reference resistors
    int referenceReservedChannels = 4; // 固定前4个通道给参考
    const int availableCalChannels = 20 - referenceReservedChannels;

    // Calculate batches
    m_batches = calculateBatches(deviceConfig.num_channels, availableCalChannels);

    emit logMessage(QString("设备 %1 有 %2 个通道, 需要分 %3 批校准")
                        .arg(deviceConfig.name)
                        .arg(deviceConfig.num_channels)
                        .arg(m_batches.size()));

    emit calibrationProgress(0, 0);
    setupNextBatch();
}

QVector<QVector<int>> BatchVerificationManager::calculateBatches(int totalChannels, int maxChannelsPerBatch)
{
    QVector<QVector<int>> batches;
    for (int startCh = 0; startCh < totalChannels; startCh += maxChannelsPerBatch)
    {
        QVector<int> batch;
        int endCh = qMin(startCh + maxChannelsPerBatch, totalChannels);
        for (int ch = startCh; ch < endCh; ++ch)
        {
            batch.append(ch);
        }
        batches.append(batch);
    }
    return batches;
}

void BatchVerificationManager::setupNextBatch()
{
    if (m_abortRequested)
    {
        emit logMessage("校准过程被中止");
        emit allBatchesCompleted(false);
        return;
    }

    // 优化后的逻辑：外层为批次循环，内层为阻值循环
    if (m_currentBatchIndex >= m_batches.size())
    {
        emit logMessage("所有批次校准完成！");
        emit allBatchesCompleted(true);
        emit saveResultsRequested();
        return;
    }

    auto batchChannels = m_batches[m_currentBatchIndex];
    int batchSize = batchChannels.size();
    int firstGlobalChannelIndex = batchChannels[0]; // 动态计算当前批次的读写首地址

    // Configure batch-specific device config - 包含所有阻值
    AdjDeviceConfig batchConfig = m_deviceConfig;
    batchConfig.num_channels = batchSize;
    batchConfig.switchDelay = m_deviceConfig.switchDelay; // 传递开关延迟配置
    // 保持所有参考阻值，让VerificationWorker内部循环处理
    batchConfig.ref_index = m_deviceConfig.ref_index;
    batchConfig.ref_values = m_deviceConfig.ref_values;

    int registerStride = 0;
    if (m_deviceConfig.name != "618A NTC-32-TIME")
    {
        registerStride = 4; // 默认每通道占用4个寄存器 根据设备型号设为2或4 单精度和双精度
    }
    else
    {
        registerStride = 2;
    }
    batchConfig.write_addr = m_deviceConfig.write_addr + (firstGlobalChannelIndex * registerStride);
    batchConfig.read_addr = m_deviceConfig.read_addr + (firstGlobalChannelIndex * registerStride);

    // Map channels to 1220, starting after reference channels
    batchConfig.cal_to_1220.clear();
    batchConfig.cal_to_1220.resize(batchSize);
    int startCalChannel = 21 - batchSize; // 可用非参考通道的起始地址
    for (int i = 0; i < batchSize; ++i)
    {
        batchConfig.cal_to_1220[i] = startCalChannel + i;
    }

    emit batchStarting(m_currentBatchIndex + 1, m_batches.size(), 0, batchChannels); // referenceIndex设为0，因为会处理所有阻值

    QVector<int> displayChannels;
    for (int ch : batchChannels)
    {
        displayChannels.append(ch + 1); // 通过UI提示用户连接当前批次的通道到1220
    }

    // 提示用户连接指定通道到1220 - 不再指定特定阻值，因为会处理所有阻值
    emit reconnectionRequired(displayChannels, -1); // -1表示会处理所有阻值

    // Create and configure worker
    cleanupWorker();
    m_worker = new VerificationWorker();
    m_worker->setDeviceConfig(batchConfig);
    m_worker->setCommandHandlers(m_calHandler, m_deviceHandler);
    m_worker->setBatchMode(true); // 设置为批量模式

    // Connect signals
    connect(m_worker, &VerificationWorker::calibrationFinished,
            this, &BatchVerificationManager::onBatchVerificationFinished);
    connect(m_worker, &VerificationWorker::logMessage,
            this, &BatchVerificationManager::handleWorkerMessages);
    connect(m_worker, &VerificationWorker::calibrationProgress,
            this, [this](int localChannel, int localProgress)
            { calculateAndEmitGlobalProgress(localChannel, localProgress); });
    connect(m_worker, &VerificationWorker::updateChannelData,
            this, &BatchVerificationManager::forwardChannelData);
    connect(m_worker, &VerificationWorker::saveResultsRequested,
            this, &BatchVerificationManager::handleSaveResultsRequested);

    connect(this, &BatchVerificationManager::uiUpdateFinished,
            m_worker, &VerificationWorker::uiUpdateCompleted); // 首先经由主线程中传回给BatchVerificationManager，再传回给VerificationWorker

    // Start worker in a new thread
    QThread *thread = new QThread();
    m_worker->moveToThread(thread);
    connect(thread, &QThread::started, m_worker, &VerificationWorker::startCalibration);
    connect(m_worker, &VerificationWorker::calibrationFinished, thread, &QThread::quit);
    connect(thread, &QThread::finished, m_worker, &QObject::deleteLater);
    connect(thread, &QThread::finished, thread, &QThread::deleteLater);
    thread->start();
}

void BatchVerificationManager::calculateAndEmitGlobalProgress(int localChannel, int completedSteps)
{
    // 计算全局进度：总工作量 = 所有设备通道数 * 阻值数
    int totalWork = m_deviceConfig.num_channels * m_deviceConfig.ref_index.size();
    if (totalWork <= 0)
        return;

    // 已完成的工作量：已完成批次的工作量 + 当前批次已完成的步骤数
    int completedWork = 0;

    // 已完成批次的工作量
    for (int i = 0; i < m_currentBatchIndex; i++)
    {
        completedWork += m_batches[i].size() * m_deviceConfig.ref_index.size();
    }

    // 当前批次的工作量：直接使用VerificationWorker传来的已完成步骤数
    completedWork += completedSteps;

    // 计算全局进度百分比
    int globalProgress = (completedWork * 100) / totalWork;
    globalProgress = qMin(100, qMax(0, globalProgress));

    // 获取当前正在校准的全局通道号（0-based）
    int globalChannel = localChannel;
    if (m_currentBatchIndex < m_batches.size() && localChannel < m_batches[m_currentBatchIndex].size())
    {
        globalChannel = m_batches[m_currentBatchIndex][localChannel];
    }

    qDebug() << "批次" << (m_currentBatchIndex + 1) << "通道" << (localChannel + 1)
             << "已完成步骤：" << completedSteps << "全局进度：" << globalProgress << "%"
             << "已完成工作：" << completedWork << "/" << totalWork;

    emit calibrationProgress(globalChannel, globalProgress);
}

void BatchVerificationManager::onBatchVerificationFinished(bool success)
{
    if (m_abortRequested)
    {
        emit logMessage("校准过程已中止");
        emit allBatchesCompleted(false);
        return;
    }

    if (!success)
    {
        emit logMessage(QString("批次 %1 校准失败，终止校准过程").arg(m_currentBatchIndex + 1));
        emit allBatchesCompleted(false);
        return;
    }

    if (m_worker)
    {
        emit logMessage(QString("批次 %1 校准成功! (已完成所有阻值)").arg(m_currentBatchIndex + 1));

        qDebug() << "批次切换: 当前批次" << m_currentBatchIndex << "完成，切换到批次" << (m_currentBatchIndex + 1);

        m_currentBatchIndex++;
        QThread::sleep(1); // Brief delay before next batch
        setupNextBatch();
    }
}

void BatchVerificationManager::handleWorkerMessages(const QString &message)
{
    QString prefixedMessage = QString("批次 %1/%2: %3")
                                  .arg(m_currentBatchIndex + 1)
                                  .arg(m_batches.size())
                                  .arg(message);
    emit logMessage(prefixedMessage);
}

void BatchVerificationManager::forwardChannelData(double referenceValue, int localChannel, QVector<double> resistances,
                                                  double measuredResistance, double deviationFromReference,
                                                  double allowedDeviation, double equivalentTempDeviation, bool calibrationResult)
{
    if (m_currentBatchIndex < m_batches.size() && localChannel <= m_batches[m_currentBatchIndex].size())
    {                                                                             // localChannel 从1开始
        int globalChannel = m_batches[m_currentBatchIndex][localChannel - 1] + 1; // +1 是因为m_batches存放的批次通道值从0开始
        qDebug() << "globalChannel: " << globalChannel;
        emit updateChannelData(referenceValue, globalChannel, resistances, measuredResistance,
                               deviationFromReference, allowedDeviation, equivalentTempDeviation, calibrationResult);
    }
}

void BatchVerificationManager::handleSaveResultsRequested()
{
    // 这里是每个批次完成后需要保存结果所调用，实际在所有批次完成后再emit saveResultsRequested()
    // emit saveResultsRequested();
}

void BatchVerificationManager::onBatchVerificationUiUpdateCompleted()
{
    emit uiUpdateFinished();
}

void BatchVerificationManager::abortVerification()
{
    m_abortRequested = true;
    // emit logMessage("Aborting batch verification...");
    if (m_worker)
    {
        m_worker->abortVerification();
    }
}

void BatchVerificationManager::recalibrateChannel(int groupIndex, double referenceValue, int channelToRecalibrate)
{
    cleanupWorker();

    // 注释部分都是worker线程中自己处理或用不到的
    AdjDeviceConfig recalConfig = m_deviceConfig;
    recalConfig.num_channels = 1;
    recalConfig.switchDelay = m_deviceConfig.switchDelay; // 保持开关延迟配置
    // recalConfig.ref_index = {m_deviceConfig.ref_index[groupIndex]};
    recalConfig.ref_values = {referenceValue};

    // int registerStride = 4;
    // recalConfig.write_addr = m_deviceConfig.write_addr + (channelToRecalibrate * registerStride);
    // recalConfig.read_addr = m_deviceConfig.read_addr + (channelToRecalibrate * registerStride);

    for (int i = 0; i < m_deviceConfig.num_channels; ++i)
    {
        recalConfig.cal_to_1220[i] = 6; // 将所有通道每个元素的值替换为6
    }

    QString message = QString("准备复校设备通道 %1:\n\n"
                              "请将设备通道 %1 连接到 1220 的通道 6\n"
                              "请确保当前需要复校的 参考电阻 连接到 1220 的通道 %2")
                          .arg(channelToRecalibrate)
                          .arg(recalConfig.ref_index[groupIndex]);

    QWidget *parentWidget = QApplication::activeWindow();
    QMessageBox connectionPrompt(QMessageBox::Information, "复校连接提示", message,
                                 QMessageBox::Ok | QMessageBox::Cancel, parentWidget);
    if (connectionPrompt.exec() != QMessageBox::Ok)
    {
        emit logMessage("复校操作已取消");
        emit allBatchesCompleted(false);
        return;
    }

    m_worker = new VerificationWorker();
    m_worker->setDeviceConfig(recalConfig);
    m_worker->setCommandHandlers(m_calHandler, m_deviceHandler);
    m_worker->setBatchMode(false); // 复校使用非批量模式

    connect(m_worker, &VerificationWorker::calibrationFinished, this, &BatchVerificationManager::onRecalibrationFinished);
    connect(m_worker, &VerificationWorker::logMessage, this, &BatchVerificationManager::handleRecalibrationMessages);
    connect(m_worker, &VerificationWorker::updateChannelData,
            [this](double refVal, int ch, QVector<double> res, double measRes, double devRef, double allowDev, double tempDev, bool result)
            {
                emit updateChannelData(refVal, ch, res, measRes, devRef, allowDev, tempDev, result);
            });
    connect(this, &BatchVerificationManager::uiUpdateFinished, m_worker, &VerificationWorker::uiUpdateCompleted);

    QThread *thread = new QThread();
    m_worker->moveToThread(thread);
    connect(thread, &QThread::started, [this, groupIndex, referenceValue, channelToRecalibrate]()
            { m_worker->restartCalibration(groupIndex, referenceValue, channelToRecalibrate); });
    connect(m_worker, &VerificationWorker::calibrationFinished, thread, &QThread::quit);
    connect(thread, &QThread::finished, m_worker, &QObject::deleteLater);
    connect(thread, &QThread::finished, thread, &QThread::deleteLater);

    m_recalibratingChannel = channelToRecalibrate;
    m_recalibratingGroupIndex = groupIndex;
    m_recalibratingReferenceValue = referenceValue;

    emit logMessage(QString("开始为通道 %1 使用参考值 %2 进行重新校准")
                        .arg(channelToRecalibrate)
                        .arg(referenceValue));
    thread->start();
}

void BatchVerificationManager::onRecalibrationFinished(bool success)
{
    emit allBatchesCompleted(success);
    emit logMessage(success ? QString("通道 %1 复校成功").arg(m_recalibratingChannel)
                            : QString("通道 %1 复校失败").arg(m_recalibratingChannel));
    cleanupWorker();
}

void BatchVerificationManager::handleRecalibrationMessages(const QString &message)
{
    emit logMessage(QString("复校通道 %1: %2").arg(m_recalibratingChannel).arg(message));
}

/*
name=618-32;(双精度类型)
num_channels = 32;
ref_index = {3,2,1};
ref_values = {1k,10k,20k};
write_addr = 0X01A5;
read_addr = 0X0059;

m_currentBatchIndex = 0;（某个参考阻值的 当前批次）
m_currentReferenceIndex = 0;（当前参考阻值下标）

referenceReservedChannels = 3；
availableCalChannels = 20-referenceReservedChannels=17；
m_batches = {
    {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16},--17
    {17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31},--15
};
1、
if (m_currentReferenceIndex >= m_deviceConfig.ref_ index.size()) 0 >= 3
if (m_currentBatchIndex >= m_batches.size()) 0>=2
batchChannels = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16};
firstGlobalChannelIndex = 0;

batchConfig
    name=618-32;(双精度类型)
    num_channels = batchSize = 17;
    ref_index = m_deviceConfig.ref_index[m_currentReferenceIndex]={3};
    ref_values = {1k};
    write_addr = 0X01A5;
    read_addr = 0X0059;
    cal_to_1220 = {4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20}--17
------
m_currentBatchIndex = 1;（某个参考阻值的 当前批次）
m_currentReferenceIndex = 0;（当前参考阻值下标）
-----
2、
if (m_currentReferenceIndex >= m_deviceConfig.ref_ index.size()) 0 >= 3 X
if (m_currentBatchIndex >= m_batches.size()) 1>=2 X
batchChannels={17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31},-15
firstGlobalChannelIndex = 17;

batchConfig
    name=618-32;(双精度类型)
    num_channels = batchSize = 15;
    ref_index = m_deviceConfig.ref_index[m_currentReferenceIndex]={3};
    ref_values = {1k};
    write_addr = 0X01A5 + xx;
    read_addr = 0X0059 + xx;
    cal_to_1220 = {6,7,8,9,10,11,12,13,14,15,16,17,18,19,20}--15 优先后面通道
------
m_currentBatchIndex = 2;（某个参考阻值的 当前批次）
m_currentReferenceIndex = 0;（当前参考阻值下标）
-----
3、
if (m_currentReferenceIndex >= m_deviceConfig.ref_ index.size()) 0 >= 3 X
if (m_currentBatchIndex >= m_batches.size()) 2>=2     ->
------
m_currentBatchIndex = 0;
m_currentReferenceIndex = 1;（当前参考阻值下标）
-----
4、
if (m_currentReferenceIndex >= m_deviceConfig.ref_ index.size()) 1 >= 3 X
if (m_currentBatchIndex >= m_batches.size()) 0>=2 X
batchChannels = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16};
firstGlobalChannelIndex = 0;

batchConfig
    name=618-32;(双精度类型)
    num_channels = batchSize = 17;
    ref_index = m_deviceConfig.ref_index[m_currentReferenceIndex]={2};
    ref_values = {2k};
    write_addr = 0X01A5;
    read_addr = 0X0059;
    cal_to_1220 = {4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20}--17
------
m_currentBatchIndex = 1;
m_currentReferenceIndex = 1;（当前参考阻值下标）
-----
......
m_currentBatchIndex = 2 >= m_batches.size()
m_currentReferenceIndex = 3 >=  m_deviceConfig.ref_index.size()
---结束
*/
