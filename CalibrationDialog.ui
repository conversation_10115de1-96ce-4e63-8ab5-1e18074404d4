<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>CalibrationDialog</class>
 <widget class="QDialog" name="CalibrationDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1050</width>
    <height>704</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Dialog</string>
  </property>
  <layout class="QGridLayout" name="gridLayout_5" columnstretch="1,1,1">
   <item row="3" column="0" colspan="2">
    <spacer name="horizontalSpacer_5">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>40</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="0" column="0">
    <widget class="QGroupBox" name="groupBox">
     <property name="title">
      <string>系统参数</string>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_2">
      <item>
       <widget class="QWidget" name="widget" native="true">
        <layout class="QHBoxLayout" name="horizontalLayout">
         <item>
          <layout class="QGridLayout" name="gridLayout" columnstretch="1,1,1,1">
           <item row="1" column="3">
            <widget class="QLineEdit" name="lineEdit_2">
             <property name="minimumSize">
              <size>
               <width>88</width>
               <height>0</height>
              </size>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="12" column="1">
            <widget class="QComboBox" name="refers_CalDialog"/>
           </item>
           <item row="3" column="0">
            <widget class="QCheckBox" name="deviceAddr_CalDialog">
             <property name="text">
              <string>设备地址</string>
             </property>
            </widget>
           </item>
           <item row="1" column="1">
            <widget class="QLineEdit" name="serialNumEdit_Cal">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>88</width>
               <height>0</height>
              </size>
             </property>
            </widget>
           </item>
           <item row="10" column="0">
            <widget class="QLabel" name="label_4">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="text">
              <string>通道：</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item row="5" column="2">
            <widget class="QCheckBox" name="boardCardModel_CalDialog">
             <property name="text">
              <string>板卡精度</string>
             </property>
            </widget>
           </item>
           <item row="11" column="3">
            <widget class="QPushButton" name="btnEndCal">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>25</height>
              </size>
             </property>
             <property name="text">
              <string>结束采集</string>
             </property>
            </widget>
           </item>
           <item row="1" column="2">
            <widget class="QCheckBox" name="systemTime_CalDialog">
             <property name="text">
              <string>系统时间</string>
             </property>
            </widget>
           </item>
           <item row="3" column="3">
            <widget class="QPushButton" name="writeBtn_CalDialog">
             <property name="text">
              <string>写入</string>
             </property>
            </widget>
           </item>
           <item row="11" column="1">
            <widget class="QLabel" name="labelStatus">
             <property name="styleSheet">
              <string notr="true">    QLabel {
        background-color: #E74C3C; /* 扁平颜色 */
        color: white;              /* 文本颜色 */
        font-size: 12px;           /* 字体大小 */
        padding: 4px;              /* 内边距，减小至 3px */
        border-radius: 5px;        /* 添加圆角 */
        border: none;              /* 无边框 */
        margin: 3px;               /* 外边距，减小至 2px */
    }</string>
             </property>
             <property name="text">
              <string>未开始</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignCenter</set>
             </property>
            </widget>
           </item>
           <item row="10" column="1">
            <widget class="QComboBox" name="chs_CalDialog">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
            </widget>
           </item>
           <item row="5" column="0">
            <widget class="QCheckBox" name="boardCardNumber_CalDialog">
             <property name="text">
              <string>板卡编号</string>
             </property>
            </widget>
           </item>
           <item row="9" column="0" colspan="4">
            <widget class="Line" name="line">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
            </widget>
           </item>
           <item row="1" column="0">
            <widget class="QCheckBox" name="serialNum_CalDialog">
             <property name="text">
              <string>序列号</string>
             </property>
            </widget>
           </item>
           <item row="5" column="3">
            <widget class="QLineEdit" name="boardCardModelEdit_CalDialog">
             <property name="placeholderText">
              <string>只读</string>
             </property>
            </widget>
           </item>
           <item row="12" column="2" colspan="2">
            <spacer name="horizontalSpacer">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
           <item row="0" column="2">
            <widget class="QPushButton" name="enterTransparentMode">
             <property name="enabled">
              <bool>true</bool>
             </property>
             <property name="text">
              <string>进入透传</string>
             </property>
            </widget>
           </item>
           <item row="4" column="0" colspan="4">
            <widget class="Line" name="line_2">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
            </widget>
           </item>
           <item row="10" column="3">
            <widget class="QPushButton" name="init_CalDialog">
             <property name="enabled">
              <bool>false</bool>
             </property>
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
             <property name="text">
              <string>设备初始化</string>
             </property>
            </widget>
           </item>
           <item row="12" column="0">
            <widget class="QLabel" name="label_5">
             <property name="text">
              <string>选择参考：</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item row="2" column="0">
            <widget class="QCheckBox" name="filterFrequency_CalDialog">
             <property name="text">
              <string>滤波次数</string>
             </property>
            </widget>
           </item>
           <item row="2" column="1">
            <widget class="QLineEdit" name="filterNum_CalDialog"/>
           </item>
           <item row="5" column="1">
            <widget class="QLineEdit" name="boardCardNumberEdit_CalDialog">
             <property name="placeholderText">
              <string>可读/写</string>
             </property>
            </widget>
           </item>
           <item row="3" column="1">
            <widget class="QComboBox" name="setAddr_CalDialog">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
            </widget>
           </item>
           <item row="0" column="1">
            <widget class="QLineEdit" name="deviceModel_CalDialog">
             <property name="minimumSize">
              <size>
               <width>88</width>
               <height>0</height>
              </size>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="2" column="2">
            <widget class="QCheckBox" name="baudRate_CalDialog">
             <property name="text">
              <string>波特率</string>
             </property>
            </widget>
           </item>
           <item row="10" column="2">
            <widget class="QPushButton" name="reset_CalDialog">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>25</height>
              </size>
             </property>
             <property name="text">
              <string>通道复位</string>
             </property>
            </widget>
           </item>
           <item row="0" column="0">
            <widget class="QLabel" name="label_3">
             <property name="text">
              <string>设备型号：</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item row="11" column="2">
            <widget class="QPushButton" name="btnStartCal">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>25</height>
              </size>
             </property>
             <property name="text">
              <string>开始采集</string>
             </property>
            </widget>
           </item>
           <item row="3" column="2">
            <widget class="QPushButton" name="readBtn_CalDialog">
             <property name="text">
              <string>读取</string>
             </property>
            </widget>
           </item>
           <item row="2" column="3">
            <widget class="QComboBox" name="baudNum_CalDialog">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
            </widget>
           </item>
           <item row="11" column="0">
            <widget class="QLabel" name="label">
             <property name="text">
              <string>采集状态：</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item row="0" column="3">
            <widget class="QPushButton" name="quitTransparentMode">
             <property name="text">
              <string>退出透传</string>
             </property>
            </widget>
           </item>
           <item row="6" column="0">
            <widget class="QLabel" name="boardCardModel_Label_CalDialog">
             <property name="text">
              <string>板卡型号：</string>
             </property>
            </widget>
           </item>
           <item row="6" column="1">
            <widget class="QComboBox" name="boardCardModel_Combo_CalDialog"/>
           </item>
           <item row="6" column="2">
            <widget class="QCheckBox" name="filterFrequency_CalDialog_1618A">
             <property name="text">
              <string>滤波次数</string>
             </property>
            </widget>
           </item>
           <item row="6" column="3">
            <widget class="QLineEdit" name="filterNum_CalDialog_1618A"/>
           </item>
          </layout>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item row="1" column="0" colspan="3">
    <widget class="QGroupBox" name="groupBox_2">
     <layout class="QGridLayout" name="gridLayout_2">
      <item row="0" column="3">
       <widget class="QPushButton" name="startCal_CalDialog">
        <property name="text">
         <string>开始标定</string>
        </property>
       </widget>
      </item>
      <item row="0" column="0">
       <widget class="QLabel" name="label_2">
        <property name="text">
         <string>电阻标定数据</string>
        </property>
       </widget>
      </item>
      <item row="0" column="2">
       <widget class="QProgressBar" name="progressBar">
        <property name="value">
         <number>0</number>
        </property>
       </widget>
      </item>
      <item row="0" column="5">
       <widget class="QPushButton" name="restartCal_CalDialog">
        <property name="text">
         <string>重新标定</string>
        </property>
       </widget>
      </item>
      <item row="0" column="4">
       <widget class="QPushButton" name="abortButton">
        <property name="text">
         <string>停止标定</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <spacer name="horizontalSpacer_2">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="0" column="6">
       <widget class="QPushButton" name="endCal_CalDialog">
        <property name="text">
         <string>保存</string>
        </property>
       </widget>
      </item>
      <item row="1" column="0" colspan="7">
       <widget class="QTableWidget" name="readDataTable"/>
      </item>
     </layout>
    </widget>
   </item>
   <item row="0" column="2">
    <widget class="QGroupBox" name="groupBox_4">
     <layout class="QGridLayout" name="gridLayout_4">
      <item row="0" column="0">
       <widget class="QLabel" name="label_7">
        <property name="text">
         <string>标定历史记录</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <spacer name="horizontalSpacer_4">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>212</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="0" column="2">
       <widget class="QPushButton" name="historyDelBtn">
        <property name="text">
         <string>删除</string>
        </property>
       </widget>
      </item>
      <item row="1" column="0" colspan="3">
       <widget class="QListWidget" name="historyListWidget">
        <property name="minimumSize">
         <size>
          <width>300</width>
          <height>0</height>
         </size>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item row="3" column="2">
    <widget class="QDialogButtonBox" name="buttonBox">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="standardButtons">
      <set>QDialogButtonBox::Cancel|QDialogButtonBox::Ok</set>
     </property>
    </widget>
   </item>
   <item row="0" column="1">
    <widget class="QGroupBox" name="groupBox_3">
     <layout class="QGridLayout" name="gridLayout_3">
      <item row="0" column="0">
       <widget class="QLabel" name="label_6">
        <property name="text">
         <string>标定日志信息</string>
        </property>
       </widget>
      </item>
      <item row="1" column="0" colspan="2">
       <widget class="QTextEdit" name="textEdit">
        <property name="minimumSize">
         <size>
          <width>300</width>
          <height>0</height>
         </size>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <spacer name="horizontalSpacer_3">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>179</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
   <item row="2" column="0" colspan="3">
    <widget class="QGroupBox" name="groupBox_5">
     <layout class="QGridLayout" name="gridLayout_6">
      <item row="0" column="0">
       <widget class="QLabel" name="label_8">
        <property name="text">
         <string>电压标定数据</string>
        </property>
       </widget>
      </item>
      <item row="0" column="3">
       <widget class="QPushButton" name="startCal_CalDialog_2">
        <property name="text">
         <string>手动标定</string>
        </property>
       </widget>
      </item>
      <item row="0" column="4">
       <widget class="QPushButton" name="abortButton_2">
        <property name="text">
         <string>停止标定</string>
        </property>
       </widget>
      </item>
      <item row="1" column="0" colspan="6">
       <widget class="QTableWidget" name="readDataTable_2"/>
      </item>
      <item row="0" column="2">
       <widget class="QProgressBar" name="progressBar_2">
        <property name="value">
         <number>0</number>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <spacer name="horizontalSpacer_6">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections>
  <connection>
   <sender>buttonBox</sender>
   <signal>accepted()</signal>
   <receiver>CalibrationDialog</receiver>
   <slot>accept()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>248</x>
     <y>254</y>
    </hint>
    <hint type="destinationlabel">
     <x>157</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>buttonBox</sender>
   <signal>rejected()</signal>
   <receiver>CalibrationDialog</receiver>
   <slot>reject()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>316</x>
     <y>260</y>
    </hint>
    <hint type="destinationlabel">
     <x>286</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
