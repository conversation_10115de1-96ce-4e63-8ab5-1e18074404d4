#pragma once

#include <QObject>
#include <QVector>
#include "calibrationworker.h"

class BatchCalibrationManager : public QObject
{
    Q_OBJECT

public:
    explicit BatchCalibrationManager(QObject *parent = nullptr);
    ~BatchCalibrationManager();

    // 设置命令发送函数指针
    typedef QPair<bool, QString> (*CommandHandlerFunc)(const QByteArray&, const QString&);
    void setCommandHandlers(CommandHandlerFunc calHandler, CommandHandlerFunc deviceHandler);

    // Start calibration for a device with more channels than 1220 can handle at once
    void startBatchCalibration(const CalDeviceConfig &deviceConfig,
                               CommandHandlerFunc calHandler,
                               CommandHandlerFunc deviceHandler);

    void abortCalibration();

public slots:
    void recalibrateChannel(int channelToRecalibrate);

signals:
    void logMessage(const QString &message);
    void allBatchesCompleted(bool success);
    void batchCompleted(int batchIndex, bool success);
    void calibrationProgress(int channel, int progress);
    void updateChannelData(int globalChannel, double resistance, double deviation, bool passed);
    void saveResultsRequested(const CalDeviceConfig &config, const QVector<QPair<double, double>> &results);        // 发送自己的给mainwindow
    void reconnectionRequired(const QVector<int> &channelsToConnect);
    void waitingForUserConfirmation(int batchIndex);
    void batchStarting(int batchIndex, int totalBatches, const QVector<int> &channelsInBatch);

private slots:
    void onBatchCalibrationFinished(bool success);
    void handleWorkerMessages(const QString &message);
    void forwardChannelData(int localChannel, double resistance, double deviation, bool passed);
    //void handleSaveResultsRequested(const CalDeviceConfig &config, const QVector<QPair<double, double>> &results); // 处理来自worker的

private:
    void setupNextBatch();
    QVector<QVector<int>> calculateBatches(int totalChannels, int maxChannelsPerBatch);
    void collectBatchResults(const QVector<QPair<double, double>> &batchResults);

    CalDeviceConfig m_deviceConfig;
    QVector<QPair<double, double>> m_allResults;
    CommandHandlerFunc m_calHandler;
    CommandHandlerFunc m_deviceHandler;
    CalibrationWorker *m_worker;

    QVector<QVector<int>> m_batches; // Each batch is a vector of channel indices
    int m_currentBatchIndex;
    bool m_abortRequested;

    // For batch retry handling
    int m_batchRetryCount;
    const int m_maxBatchRetries = 1; // 单个批次最大重试次数

    // 计算并发送全局进度
    void calculateAndEmitGlobalProgress(int localChannel, int localProgress);

    // 处理复校相关方法
    void onRecalibrationFinished(bool success);
    void handleRecalibrationMessages(const QString &message);

    // 添加成员变量
    int m_recalibratingChannel = -1; // 当前正在复校的通道

    void cleanupWorker();
};
