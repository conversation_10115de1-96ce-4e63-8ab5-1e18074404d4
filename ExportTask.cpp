﻿#include "ExportTask.h"
#include <QDir>
#include <QCoreApplication>
#include <QTemporaryFile>
#include <QFile>
#include <QIODevice>
#include <QDebug>
#include <memory> // for std::make_unique

// ExportTask::ExportTask(const QVariantMap& data) : data(data) {}
ExportTask::ExportTask(const QString &savePath, const ProjectData &projectData) : savePath(savePath), projectData(projectData) {}

QString ExportTask::createTemporaryFileFromResource(const QString &resourcePath)
{
    m_tempFile = std::make_unique<QTemporaryFile>();
    if (m_tempFile->open())
    {
        QFile resourceFile(resourcePath);
        if (resourceFile.open(QIODevice::ReadOnly))
        {
            m_tempFile->write(resourceFile.readAll());
            resourceFile.close();
            m_tempFile->flush();
            m_tempFile->close();
        }
        else
        {
            qDebug() << "Failed to open resource file!";
            return QString();
        }
    }
    else
    {
        qDebug() << "Failed to create temporary file!";
        return QString();
    }

    QString templatePath = m_tempFile->fileName();
    qDebug() << "Template path:" << templatePath; // 打印临时文件路径进行调试

    return templatePath;
}

// 生成设备信息映射
QMap<QString, SensorInfo> createSensorInfoMap()
{
    QMap<QString, SensorInfo> sensorInfoMap;

    // 所有设备型号对应的传感器信息
    sensorInfoMap["618A"] = {"热敏电阻", "10kΩ"};
    sensorInfoMap["619A PLUS"] = {"热敏电阻", "10kΩ"};
    sensorInfoMap["618A PLUS"] = {"热敏电阻", "10kΩ"};
    sensorInfoMap["618A PLUS(6NTC+2P-AP23)"] = {"热敏电阻", "10kΩ"};
    sensorInfoMap["618A RTD"] = {"铂电阻", "100Ω"};
    sensorInfoMap["618A RTD PLUS"] = {"铂电阻", "100Ω"};
    sensorInfoMap["619A RTD PLUS"] = {"铂电阻", "100Ω"};

    sensorInfoMap["TM14RD-PT100"] = {"铂电阻", "100Ω"};
    sensorInfoMap["TM14RD-PT1000"] = {"铂电阻", "1000Ω"};

    sensorInfoMap["TM14ND"] = {"热敏电阻", "10kΩ"};
    sensorInfoMap["TM14ND-T"] = {"热敏电阻", "10kΩ"};
    sensorInfoMap["TM14ND-P"] = {"热敏电阻", "10kΩ"};
    sensorInfoMap["TM14ND-P-S"] = {"热敏电阻", "10kΩ"};
    sensorInfoMap["TM24ND-P-S"] = {"热敏电阻", "10kΩ"};

    sensorInfoMap["TM18ND-P"] = {"热敏电阻", "N/A"}; // TM18ND-P报告中无需体现标称电阻
    sensorInfoMap["TM18RD-P"] = {"铂电阻", "N/A"};   // TM18RD-P报告中无需体现标称电阻

    sensorInfoMap["H-LCW-22B"] = {"热敏电阻", "10kΩ"};

    sensorInfoMap["618A NTC-32"] = {"热敏电阻", "10kΩ"};
    sensorInfoMap["618A NTC-32-TIME"] = {"热敏电阻", "10kΩ"};

    sensorInfoMap["619A NTC-32 PLUS"] = {"热敏电阻", "10kΩ"};
    sensorInfoMap["619A RTD-32 PLUS"] = {"铂电阻", "100Ω"};

    sensorInfoMap["1611A-HT(PT100)"] = {"铂电阻", "100Ω"};
    sensorInfoMap["1611A-HT(PT1000)"] = {"铂电阻", "1000Ω"};

    return sensorInfoMap;
}

// 生成校准方法描述
QString generateCalibrationMethodDescription(const ProjectData &projectData)
{

    // 获取传感器信息
    QMap<QString, SensorInfo> sensorInfoMap = createSensorInfoMap();

    SensorInfo info;

    if (projectData.deviceType == "1618A-NTC板卡")
    {
        info = {"热敏电阻", "10kΩ"};
    }
    else if (projectData.deviceType == "1618A-RTD板卡")
    {
        info = {"铂电阻", "1000Ω、100Ω"};
    }
    else
    {
        if (!sensorInfoMap.contains(projectData.deviceModel))
        {
            return "未知设备型号";
        }
        info = sensorInfoMap[projectData.deviceModel];
    }

    // 检测是否为TM18系列设备类型（TM18ND或TM18RD）
    bool isTM18SeriesType = projectData.deviceModel.contains("TM18ND") || projectData.deviceModel.contains("TM18RD");

    if (isTM18SeriesType)
    {
        // TM18系列设备的校准方法描述（不包含等效温度偏差）
        return QString("%1的温度测量（%2温度测量）功能，通过测量参考电阻值偏差进行校准。\n"
                       "校准数据包含参考阻值、测量阻值、阻值偏差、允许偏差、校准结果。"
                       "参考阻值是使用校准设备测量的参考电阻阻值，测量阻值是使用%1测量的参考电阻阻值，"
                       "阻值偏差是校准设备测量值与%1测量值的差值，"
                       "允许偏差是%1给定的阻值准确度，校准结果评定为通过(P)、不通过(F)。")
            .arg(projectData.deviceType)
            .arg(info.sensorTypeName);
    }
    else
    {
        // 标准设备的校准方法描述（包含等效温度偏差）
        return QString("%1的温度测量（%2温度测量）功能，通过测量参考电阻值偏差，等效换算为%2温度传感器（标称电阻为%3）温度偏差进行校准。\n"
                       "校准数据包含参考阻值、测量阻值、阻值偏差、等效温度偏差、允许偏差、校准结果。"
                       "参考阻值是使用校准设备测量的参考电阻阻值，测量阻值是使用%1测量的参考电阻阻值，"
                       "阻值偏差是校准设备测量值与%1测量值的差值，等效温度偏差是将阻值偏差根据%2阻值-温度表换算得出，"
                       "允许偏差是%1给定的温度准确度，校准结果评定为通过(P)、不通过(F)。")
            .arg(projectData.deviceType)
            .arg(info.sensorTypeName)
            .arg(info.resistance);
    }
}

void ExportTask::process()
{
    CoInitialize(NULL);

    // 检查保存路径是否有效
    if (savePath.isEmpty())
    {
        emit finished(false, "");
        return;
    }

    // 确定文件格式
    bool isPdf = savePath.toLower().endsWith(".pdf");

    // QString resourcePath = ":/Tmpl.dotx";
    QString resourcePath = ":/Tmpl_v4.dotx";
    m_templatePath = createTemporaryFileFromResource(resourcePath); // 注意该路径下的临时文件至少需要导出报告后才能销毁

    // 新建一个word应用程序
    QAxObject *word = new QAxObject("Word.Application");

    // 并设置为不可见
    word->setProperty("Visible", false);
    // 获取所有的工作文档
    QAxObject *documents = word->querySubObject("Documents");
    connect(documents, SIGNAL(exception(int, QString, QString, QString)), this, SLOT(slots_exception(int, QString, QString, QString)));

    // 以模板新建一个文档
    documents->dynamicCall("Add(QString)", m_templatePath);

    // 获取当前激活的文档
    QAxObject *document = word->querySubObject("ActiveDocument");
    // connect(document, SIGNAL(exception(int, QString, QString, QString)), this, SLOT(slots_exception(int, QString, QString, QString)));

    AmbientInfo ambientInfo = projectData.ambientInfo;

    // 使用传递过来的数据替换书签
    replaceBookmarkText(document, "lineEdit1", projectData.deviceSerialNumber);
    replaceBookmarkText(document, "reportNum", ambientInfo.reportNum);
    replaceBookmarkText(document, "ambientTemp", ambientInfo.temp);
    replaceBookmarkText(document, "ambientHum", ambientInfo.hum);

    if (projectData.deviceModel == "1611A-HT(PT100)" || projectData.deviceModel == "1611A-HT(PT1000)")
    {
        replaceBookmarkText(document, "deviceModel", "1611A-HT");
    }
    else
    {
        replaceBookmarkText(document, "deviceModel", projectData.deviceModel);
    }

    replaceBookmarkText(document, "ambientPre", ambientInfo.pre);
    replaceBookmarkText(document, "calDate", projectData.calibrationDate.toString("yyyy年MM月dd日"));

    // QString calMethod = calMethods.value(projectData.deviceModel, "未知设备型号");

    // 生成校准方法描述
    QString calMethod = generateCalibrationMethodDescription(projectData);

    replaceBookmarkText(document, "calMethod1", calMethod);

    replaceBookmarkText(document, "calibrator", ambientInfo.calibrator);
    replaceBookmarkText(document, "calDate2", projectData.calibrationDate.toString("yyyy年MM月dd日"));
    replaceBookmarkText(document, "deviceName", projectData.deviceType);

    // 辅助校准设备新增行
    QAxObject *table = document->querySubObject("Tables(int)", 2); // 获取文档中的第二个表
    QAxObject *rows = table->querySubObject("Rows");

    foreach (const DeviceInfo &device, projectData.calDeviceInfo)
    {
        QVariantMap rowData;
        rowData["checkBoxText"] = device.type;
        rowData["deviceModelText"] = device.model;
        rowData["serialNumText"] = device.serialNumber;
        rowData["dateTimeText"] = device.calibrationDate;

        addRowToTable(rows, rowData);
    }

    // 清理
    delete rows;
    delete table;

    // 获取 Word 文档的书签位置
    QAxObject *bookmark = document->querySubObject("Bookmarks(QString)", "testData");
    QAxObject *range = bookmark->querySubObject("Range()"); // 从ProjectData计算总行数
    int numRows = 0;
    for (const ReferenceData &refData : projectData.referenceValues)
    {
        numRows += refData.channels.size();
    }
    QAxObject *tables = document->querySubObject("Tables");

    // 检测是否为TM18系列设备类型（TM18ND或TM18RD）
    bool isTM18SeriesType = projectData.deviceModel.contains("TM18ND") || projectData.deviceModel.contains("TM18RD");

    // 根据设备类型确定列数和表头
    int columnCount = isTM18SeriesType ? 6 : 7;
    QAxObject *newTable = tables->querySubObject("Add(QVariant, int, int)", range->asVariant(), numRows + 1, columnCount);

    QStringList headers;

    if (isTM18SeriesType)
    {
        // TM18系列设备使用6列表头（无等效温度偏差列）
        headers = QStringList({"通道",
                               "参考阻值(Ω)",
                               "测量阻值(Ω)",
                               "阻值偏差(Ω)",
                               "允许偏差(Ω)",
                               "校准结果"});
    }
    else
    {
        // 标准设备使用7列表头
        headers = QStringList({"通道",
                               "参考阻值(Ω)",
                               "测量阻值(Ω)",
                               "阻值偏差(Ω)",
                               "等效温度偏差(mK)",
                               "允许偏差(mK)",
                               "校准结果"});
    }

    for (int i = 0; i < headers.size(); ++i)
    {
        QAxObject *cell = newTable->querySubObject("Cell(Row, Column)", 1, i + 1);
        QAxObject *range = cell->querySubObject("Range");
        range->setProperty("Text", headers[i]);

        QAxObject *font = range->querySubObject("Font");
        font->setProperty("Name", "宋体");
        font->setProperty("Size", 9);
        font->setProperty("Bold", true);

        QAxObject *borders = cell->querySubObject("Borders");
        for (int j = 1; j <= 4; ++j)
        { // 1-4 表示上下左右边框
            QAxObject *border = borders->querySubObject("Item(int)", j);
            border->setProperty("LineStyle", 1); // 1表示单实线
        }

        // 设置单元格内容居中
        QAxObject *paragraphFormat = range->querySubObject("ParagraphFormat");
        paragraphFormat->setProperty("Alignment", 1); // 1表示居中对齐
    }

    int rowIndex = 2; // Iterate through all reference values in the project
    for (const ReferenceData &referenceData : projectData.referenceValues)
    {
        // Iterate through all channels for this reference value
        for (const ChannelData &channelData : referenceData.channels)
        {
            for (int col = 1; col <= columnCount; ++col)
            {
                QAxObject *cell = newTable->querySubObject("Cell(Row, Column)", rowIndex, col);
                QAxObject *range = cell->querySubObject("Range");

                if (isTM18SeriesType)
                {
                    // TM18系列设备6列格式
                    switch (col)
                    {
                    case 1:
                    {
                        // Convert channel number to T1, T2, etc. format
                        QString channelStr = QString("T%1").arg(channelData.channelNumber);
                        range->setProperty("Text", channelStr);
                        break;
                    }
                    case 2:
                    {
                        // Format reference value to 5 decimal places
                        QString refResFormatted = QString::number(referenceData.referenceValue, 'f', 4);
                        range->setProperty("Text", refResFormatted);
                        break;
                    }
                    case 3:
                    {
                        // Format measured resistance to 5 decimal places
                        QString measResFormatted = QString::number(channelData.measuredResistance, 'f', 4);
                        range->setProperty("Text", measResFormatted);
                        break;
                    }
                    case 4:
                    {
                        // Format deviation with + or - sign
                        double devValue = channelData.deviationFromReference;
                        QString devStr = QString::number(devValue, 'f', 4);
                        if (devValue >= 0)
                        {
                            devStr = QString("+%1").arg(devStr);
                        }
                        range->setProperty("Text", devStr);
                        break;
                    }
                    case 5:
                    {
                        // Use the allowed deviation (in Ohms for TM18 series)
                        // 根据设备型号确定小数位数：TM18RD-P需要2位，TM18ND-P需要1位
                        int precision = (projectData.deviceModel.contains("TM18RD")) ? 2 : 1;
                        QString allowedDevStr = QString::number(channelData.allowedDeviation, 'f', precision);
                        range->setProperty("Text", allowedDevStr);
                        break;
                    }
                    case 6:
                    {
                        // Use the calibration result
                        range->setProperty("Text", channelData.calibrationResult ? "P" : "F");
                        break;
                    }
                    }
                }
                else
                {
                    // 标准设备7列格式
                    switch (col)
                    {
                    case 1:
                    {
                        // Convert channel number to T1, T2, etc. format
                        QString channelStr = QString("T%1").arg(channelData.channelNumber);
                        range->setProperty("Text", channelStr);
                        break;
                    }
                    case 2:
                    {
                        // Format reference value to 5 decimal places
                        QString refResFormatted = QString::number(referenceData.referenceValue, 'f', 4);
                        range->setProperty("Text", refResFormatted);
                        break;
                    }
                    case 3:
                    {
                        // Format measured resistance to 5 decimal places
                        QString measResFormatted = QString::number(channelData.measuredResistance, 'f', 4);
                        range->setProperty("Text", measResFormatted);
                        break;
                    }
                    case 4:
                    {
                        // Format deviation with + or - sign
                        double devValue = channelData.deviationFromReference;
                        QString devStr = QString::number(devValue, 'f', 4);
                        if (devValue >= 0)
                        {
                            devStr = QString("+%1").arg(devStr);
                        }
                        range->setProperty("Text", devStr);
                        break;
                    }
                    case 5:
                    {
                        // Use the equivalent temperature deviation directly from the data structure
                        double tempDevValue = channelData.equivalentTempDeviation;
                        QString tempDevStr = QString::number(tempDevValue, 'f', 1);
                        if (tempDevValue >= 0)
                        {
                            tempDevStr = QString("+%1").arg(tempDevStr);
                        }
                        range->setProperty("Text", tempDevStr);
                        break;
                    }
                    case 6:
                    {
                        // Use the allowed deviation directly from the data structure
                        QString allowedDevStr = QString::number(channelData.allowedDeviation, 'f', 1);
                        range->setProperty("Text", allowedDevStr);
                        break;
                    }
                    case 7:
                    {
                        // Use the calibration result directly from the data structure
                        range->setProperty("Text", channelData.calibrationResult ? "P" : "F");
                        break;
                    }
                    }
                }

                // 设置单元格样式
                setCellStyle(cell);
            }
            rowIndex++;
        }
    }

    // 设置除表头外所有单元格的边框和字体
    for (int row = 2; row <= rowIndex; ++row)
    {
        for (int col = 1; col <= columnCount; ++col)
        {
            QAxObject *cell = newTable->querySubObject("Cell(Row, Column)", row, col);
            QAxObject *range = cell->querySubObject("Range");

            QAxObject *font = range->querySubObject("Font");
            font->setProperty("Name", "宋体");
            font->setProperty("Size", 9);
            font->setProperty("Bold", false);

            QAxObject *paragraphFormat = range->querySubObject("ParagraphFormat");
            paragraphFormat->setProperty("Alignment", 1); // 1表示居中对齐

            QAxObject *borders = cell->querySubObject("Borders");
            for (int i = 1; i <= 4; ++i)
            { // 1-4 表示上下左右边框
                QAxObject *border = borders->querySubObject("Item(int)", i);
                border->setProperty("LineStyle", 1); // 1表示单实线
            }
        }
    }

    connect(newTable, SIGNAL(exception(int, QString, QString, QString)), this, SLOT(slots_exception(int, QString, QString, QString)));
    mergeReferenceValueCells(newTable, projectData.referenceValues);

    // 此处文件名应与 数据表中的calibration_date保持一致 而非调用导出函数的时间

    // 且直接弹出保存框，而非仅显示到项目列表

    // 直接保存到用户指定的位置
    if (isPdf)
    {
        document->dynamicCall("SaveAs(const QString&, int)", QDir::toNativeSeparators(savePath), 17); // 17 for PDF format
    }
    else
    {
        // 直接保存为Word
        document->dynamicCall("SaveAs(const QString&)", QDir::toNativeSeparators(savePath));
    }

    // 关闭文档和应用
    document->dynamicCall("Close(boolean)", false);
    word->dynamicCall("Quit()");

    // 确认文件存在
    bool success = QFile::exists(savePath);

    emit finished(success, savePath);

    //    // 临时保存文件到应用程序的临时目录
    //    QString deviceSerialNumber = projectData.deviceSerialNumber;
    //    QString calibrationDate = projectData.calibrationDate.toString("yyyyMMddHHmmss");

    //    // 创建新的 timestamp
    //    QString timestamp = QString("%1-%2").arg(deviceSerialNumber, calibrationDate);

    //    // 创建临时路径
    //    // 获取应用程序目录
    //    QString appDir = QCoreApplication::applicationDirPath();

    //    // 创建 TempDoc 文件夹
    //    QDir dir(appDir);
    //    if (!dir.exists("TempDoc")) {
    //        dir.mkdir("TempDoc");
    //    }

    //    // 创建临时路径
    //    QString tempPath = appDir + "/TempDoc/TempDoc_" + timestamp + ".pdf";
    //    QString tempPath_Word = appDir + "/TempDoc/TempDoc_" + timestamp + ".docx";

    //    connect(document, SIGNAL(exception(int, QString, QString, QString)), this, SLOT(slots_exception(int, QString, QString, QString)));

    //    document->dynamicCall("SaveAs(const QString&)", QDir::toNativeSeparators(tempPath_Word)); //Word
    //    document->dynamicCall("SaveAs(const QString&, int)", QDir::toNativeSeparators(tempPath), 17); //PDF
    //    document->dynamicCall("Close (boolean)", false);
    //    word->dynamicCall("Quit()");

    //    //emit finished(timestamp, tempPath);

    CoUninitialize();
}

void ExportTask::slots_exception(int code, const QString &source, const QString &desc, const QString &help)
{
    qDebug() << "Exception code:" << code;
    qDebug() << "Source:" << source;
    qDebug() << "Description:" << desc;
    qDebug() << "Help:" << help;
}

void ExportTask::MergeCells(QAxObject *table, int nStartRow, int nStartCol, int nEndRow, int nEndCol)
{
    if (nStartRow == nEndRow && nStartCol == nEndCol)
    {
        // 如果开始和结束单元格相同，则不执行合并操作
        return;
    }

    QAxObject *StartCell = table->querySubObject("Cell(int, int)", nStartRow, nStartCol);
    QAxObject *EndCell = table->querySubObject("Cell(int, int)", nEndRow, nEndCol);
    StartCell->dynamicCall("Merge(LPDISPATCH)", EndCell->asVariant());

    // After merging, set the alignment of the merged cell to center
    QAxObject *range = StartCell->querySubObject("Range");
    QAxObject *paragraphFormat = range->querySubObject("ParagraphFormat");
    paragraphFormat->setProperty("Alignment", 1); // 1 represents wdAlignParagraphCenter

    // 设置垂直居中
    QAxObject *cells = range->querySubObject("Cells");
    cells->setProperty("VerticalAlignment", 1);

    // Set font style for the merged cell
    QAxObject *font = range->querySubObject("Font");
    font->setProperty("Name", "宋体");
    font->setProperty("Size", 9);
    font->setProperty("Bold", false);

    //    QAxObject* borders = StartCell->querySubObject("Borders");
    //    for (int j = 1; j <= 4; ++j) {  // 1-4 表示上下左右边框
    //        QAxObject* border = borders->querySubObject("Item(int)", j);
    //        border->setProperty("LineStyle", 1);  // 1表示单实线
    //    }

    // Clean up
    delete font;
    delete paragraphFormat;
    delete range;
    delete StartCell;
    delete EndCell;

    qDebug() << "合并一次";
}

void ExportTask::mergeReferenceValueCells(QAxObject *table, const QVector<ReferenceData> &referenceValues)
{
    int currentRow = 2; // Start from row 2 as in the original function

    for (const ReferenceData &refData : referenceValues)
    {
        int channelCount = refData.channels.size();

        // If there's more than one channel for this reference value, we need to merge cells
        if (channelCount > 1)
        {
            // Clear the content of cells to be merged (except the first one)
            // This is consistent with the original function's approach
            for (int i = 1; i < channelCount; ++i)
            {
                QAxObject *cell = table->querySubObject("Cell(int, int)", currentRow + i, 2);
                QAxObject *range = cell->querySubObject("Range");
                range->setProperty("Text", "");
                delete range;
                delete cell;
            }

            // Merge the cells for this reference value
            MergeCells(table, currentRow, 2, currentRow + channelCount - 1, 2);
        }

        // Move to the next reference value's starting row
        currentRow += channelCount;
    }
}

void ExportTask::mergeSameRefResCellsAny(QAxObject *table, const QVariantList &measurements, int column, const QString &fieldName)
{
    // 获取表格的列数
    QAxObject *columns = table->querySubObject("Columns");
    int columnCount = columns->property("Count").toInt();
    delete columns;

    // 如果传入的列索引为负数，则转换为正数索引
    if (column < 0)
    {
        column = columnCount + column + 1;
    }

    QString prevValue;
    int startRow = 2;
    int currentRow = 2;

    foreach (const QVariant &measurementVariant, measurements)
    {
        QVariantMap measurement = measurementVariant.toMap();
        QString currentValue = measurement[fieldName].toString();

        if (currentValue == prevValue)
        {
            // 如果字段值相同，则继续，但先清空当前行指定列的值
            QAxObject *currentCell = table->querySubObject("Cell(int, int)", currentRow, column);
            QAxObject *currentRange = currentCell->querySubObject("Range");
            currentRange->setProperty("Text", "");
            delete currentRange;
            delete currentCell;

            currentRow++;
        }
        else
        {
            // 如果字段值不同，合并前一组单元格（如果有多于一行）
            if (startRow < currentRow)
            {
                MergeCells(table, startRow, column, currentRow - 1, column);
            }
            startRow = currentRow;
            currentRow++;
        }
        prevValue = currentValue;
    }

    // Merge the last group of cells if necessary
    if (startRow < currentRow)
    {
        MergeCells(table, startRow, column, currentRow - 1, column);
    }
}

void ExportTask::setParagraphFormat(QAxObject *range)
{
    QAxObject *paragraph = range->querySubObject("Paragraphs(1)");
    QAxObject *paragraphFormat = paragraph->querySubObject("Range()")->querySubObject("ParagraphFormat");
    paragraphFormat->setProperty("SpaceBefore", 0);     // 段前间距
    paragraphFormat->setProperty("SpaceAfter", 0);      // 段后间距
    paragraphFormat->setProperty("LineSpacingRule", 4); // 4表示固定行距
    paragraphFormat->setProperty("LineSpacing", 9);     // 0.75倍行距（12磅的0.75倍）
}

void ExportTask::replaceBookmarkText(QAxObject *document, const QString &bookmarkName, const QString &text)
{
    QAxObject *bookmark = document->querySubObject("Bookmarks(QVariant)", bookmarkName);
    if (!bookmark->isNull())
    {
        bookmark->dynamicCall("Select(void)");
        QAxObject *range = bookmark->querySubObject("Range");
        range->setProperty("Text", text);
        // 设置字体样式
        QAxObject *font = range->querySubObject("Font");
        font->setProperty("Name", "宋体");     // 字体名称
        font->setProperty("Size", 9);          // 字体大小
        font->setProperty("Bold", false);      // 非粗体
        font->setProperty("Italic", false);    // 非斜体
        font->setProperty("Underline", false); // 无下划线
        font->setProperty("Color", 0);         // 字体颜色为黑色
        delete font;
        delete range;
    }
    delete bookmark;
}

void ExportTask::addRowToTable(QAxObject *rows, const QVariantMap &rowData)
{
    QAxObject *row = rows->querySubObject("Add()");
    if (row)
    {
        QAxObject *cell1 = row->querySubObject("Cells(int)", 1);
        QAxObject *cell2 = row->querySubObject("Cells(int)", 2);
        QAxObject *cell3 = row->querySubObject("Cells(int)", 3);
        QAxObject *cell4 = row->querySubObject("Cells(int)", 4);

        if (cell1 && cell2 && cell3 && cell4)
        {
            cell1->querySubObject("Range")->setProperty("Text", rowData["checkBoxText"].toString());
            cell2->querySubObject("Range")->setProperty("Text", rowData["deviceModelText"].toString());
            cell3->querySubObject("Range")->setProperty("Text", rowData["serialNumText"].toString());
            cell4->querySubObject("Range")->setProperty("Text", rowData["dateTimeText"].toString());

            cell1->querySubObject("Range")->setProperty("Style", "校准设备表");
            cell2->querySubObject("Range")->setProperty("Style", "校准设备表");
            cell3->querySubObject("Range")->setProperty("Style", "校准设备表");
            cell4->querySubObject("Range")->setProperty("Style", "校准设备表");

            delete cell1;
            delete cell2;
            delete cell3;
            delete cell4;
        }

        delete row;
    }
}

void ExportTask::setCellStyle(QAxObject *cell)
{
    QAxObject *range = cell->querySubObject("Range");
    QAxObject *font = range->querySubObject("Font");
    font->setProperty("Name", "宋体");
    font->setProperty("Size", 9);
    font->setProperty("Bold", false);

    QAxObject *paragraphFormat = range->querySubObject("ParagraphFormat");
    paragraphFormat->setProperty("Alignment", 1); // 1表示居中对齐

    QAxObject *borders = cell->querySubObject("Borders");
    for (int i = 1; i <= 4; ++i)
    {
        QAxObject *border = borders->querySubObject("Item(int)", i);
        border->setProperty("LineStyle", 1); // 1表示单实线
    }

    delete font;
    delete paragraphFormat;
    delete borders;
    delete range;
}

QVariantList ExportTask::generateMeasurements(const ProjectData &projectData)
{
    QVariantList measurements;
    for (const ReferenceData &refData : projectData.referenceValues)
    {
        for (const ChannelData &channelData : refData.channels)
        {
            QVariantMap measurement;
            measurement["channel"] = QString("T%1").arg(channelData.channelNumber);              // 通道号
            measurement["refRes"] = QString::number(refData.referenceValue, 'f', 5);             // 参考阻值 (Ω)
            measurement["measRes"] = QString::number(channelData.measuredResistance, 'f', 5);    // 测量阻值 (Ω)
            measurement["devRes"] = QString::number(channelData.deviationFromReference, 'f', 5); // 阻值偏差 (mΩ)
            measurement["tempDev"] = channelData.equivalentTempDeviation;                        // 等效温度偏差 (mK)
            measurement["allowedDev"] = channelData.allowedDeviation;                            // 允许偏差 (mK)
            measurement["calibrationResult"] = channelData.calibrationResult ? "P" : "F";        // 校准结果 (P/F)
            measurements.append(measurement);
        }
    }
    return measurements;
}

// void ExportTask::mergeSameRefResCells(QAxObject* table, const QVariantList& measurements) {
//     QString prevRefRes;
//     int startRow = 2;
//     int currentRow = 2;

//    qDebug() << "measurements：" << measurements;

//        foreach (const QVariant& measurementVariant, measurements) {
//        QVariantMap measurement = measurementVariant.toMap();
//        QString currentRefRes = measurement["refRes"].toString();

//        if (currentRefRes == prevRefRes) {
//            // 如果参考阻值相同，则继续，但先清空当前行第二列的值
//            QAxObject* currentCell = table->querySubObject("Cell(int, int)", currentRow, 2);
//            QAxObject* currentRange = currentCell->querySubObject("Range");
//            currentRange->setProperty("Text", "");
//            delete currentRange;
//            delete currentCell;

//            currentRow++;
//        } else {
//            // 如果参考阻值不同，合并前一组单元格（如果有多于一行）
//            if (startRow < currentRow) {
//                MergeCells(table, startRow, 2, currentRow - 1, 2);
//            }
//            startRow = currentRow;
//            currentRow++;
//        }
//        prevRefRes = currentRefRes;
//    }

//    // Merge the last group of cells if necessary
//    if (startRow < currentRow) {
//        MergeCells(table, startRow, 2, currentRow - 1, 2);
//    }
//}
