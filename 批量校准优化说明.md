# 批量校准优化说明

## 优化目标
将32通道设备的批量校准插拔次数从9次减少到3次，提高校准效率。

## 原始逻辑
```
阻值1：批次1(ch1-15) → 批次2(ch16-30) → 批次3(ch31-32)
阻值2：批次1(ch1-15) → 批次2(ch16-30) → 批次3(ch31-32)  
阻值3：批次1(ch1-15) → 批次2(ch16-30) → 批次3(ch31-32)
总共需要9次插拔
```

## 优化后逻辑
```
批次1(ch1-15)：阻值1 → 阻值2 → 阻值3
批次2(ch16-30)：阻值1 → 阻值2 → 阻值3
批次3(ch31-32)：阻值1 → 阻值2 → 阻值3
总共只需要3次插拔
```

## 主要修改

### 1. BatchVerificationManager类修改
- **文件**: `BatchVerification.cpp`
- **主要变更**:
  - 修改`setupNextBatch()`函数，改变循环嵌套顺序
  - 外层循环：批次（3个批次）
  - 内层循环：参考阻值（由VerificationWorker处理）
  - 更新进度计算逻辑
  - 修改日志输出格式

### 2. VerificationWorker类修改  
- **文件**: `VerificationWorker.cpp`
- **主要变更**:
  - 重构`startCalibration()`方法
  - 外层循环：通道循环
  - 内层循环：参考阻值循环
  - 优化开关控制：第一个阻值时等待稳定，后续阻值快速切换
  - 改进数据存储逻辑，确保每个阻值的数据正确保存

### 3. UI交互优化
- **文件**: `mainwindow.cpp`
- **主要变更**:
  - 更新`handleReconnectionRequired_Adj()`函数
  - 优化用户提示信息，说明一次连接处理所有阻值
  - 保持与原有逻辑的兼容性

## 技术细节

### 开关控制优化
```cpp
// 只在第一个阻值时打开通道并等待稳定
if (round == 0) {
    // 打开通道并等待switchDelay秒
    // ...
} else {
    // 后续阻值只需要切换参考通道，等待2秒
    // ...
}
```

### 进度计算优化
```cpp
// 优化后的进度计算：总工作量 = 通道数 * 阻值数
int totalChannels = m_deviceConfig.num_channels * m_deviceConfig.ref_index.size();
int completedWork = 0;
for (int i = 0; i < m_currentBatchIndex; i++) {
    completedWork += m_batches[i].size() * m_deviceConfig.ref_index.size();
}
```

### 数据存储优化
- 为每个参考阻值创建独立的ReferenceData
- 确保数据按阻值正确分组存储
- 保持与原有数据库结构的兼容性

## 兼容性保证

### 复校功能
- 复校功能保持不变，仍使用独立的VerificationWorker实例
- 支持单通道复校，不受批量校准优化影响

### 16通道以下设备
- 16通道及以下设备继续使用原有的非批量校准逻辑
- 不受此次优化影响

### 数据格式
- 保持与原有数据库结构完全兼容
- UI显示格式不变
- 导出功能不受影响

## 预期效果

### 效率提升
- 32通道设备：插拔次数从9次减少到3次（减少67%）
- 48通道设备：插拔次数从12次减少到4次（减少67%）
- 大幅减少人工操作时间和出错概率

### 用户体验
- 减少重复插拔操作
- 更清晰的进度提示
- 更友好的用户界面提示

## 测试建议

1. **功能测试**
   - 测试32通道设备的批量校准
   - 验证所有阻值都能正确校准
   - 检查数据存储的正确性

2. **兼容性测试**  
   - 测试16通道以下设备的校准
   - 验证复校功能
   - 检查数据导出功能

3. **异常处理测试**
   - 测试校准过程中的中止操作
   - 验证错误处理逻辑
   - 检查UI状态恢复

## 进度更新优化

### 问题
原始实现中，进度条更新不够细致，没有在每个阻值完成时及时更新进度。

### 解决方案
```cpp
// 在每个阻值完成后更新进度
int totalSteps = m_deviceConfig.num_channels * m_deviceConfig.ref_index.size();
int currentStep = ch * m_deviceConfig.ref_index.size() + round + 1; // +1因为当前阻值已完成
int progressPercent = (currentStep * 100) / totalSteps;
emit calibrationProgress(ch, progressPercent);
```

### 效果
- 每完成一个通道的一个阻值挡位就更新一次进度
- 用户可以实时看到校准进展
- 进度更新更加平滑和准确

## 1220开关控制验证

### 关键实现
```cpp
// 在每个阻值循环中使用正确的参考通道
int referChannel = m_deviceConfig.ref_index[round];   // round=0,1,2对应不同参考通道
QByteArray openSwitchs = createModbusCommand(createModbusOpen1220Frame(referChannel, calChannel).toHex());
```

### 命令生成示例
对于通道6的校准，不同阻值会生成不同的1220开关命令：
- 阻值1 (referChannel=1): `createModbusOpen1220Frame(1, 6)` → 0x21 (0010 0001)
- 阻值2 (referChannel=2): `createModbusOpen1220Frame(2, 6)` → 0x22 (0010 0010)
- 阻值3 (referChannel=3): `createModbusOpen1220Frame(3, 6)` → 0x24 (0010 0100)

### 验证结果
✅ 每个阻值使用正确的参考通道
✅ 1220开关控制命令正确生成
✅ 位掩码计算准确无误

## 注意事项

1. 确保在第一个阻值校准时有足够的等待时间让开关稳定
2. 后续阻值切换时的2秒等待时间可根据实际情况调整
3. 如发现数据存储问题，需要检查ReferenceData的创建和管理逻辑
4. 进度计算基于总的"通道×阻值"数量，确保进度显示的准确性
5. 1220开关控制已验证正确，每个阻值使用对应的参考通道
