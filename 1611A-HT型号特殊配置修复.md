# 1611A-HT型号特殊配置修复

## 问题描述
1611A-HT型号设备在以下方面需要特殊配置：
1. **滤波次数地址**：应使用`00 03`而不是普通设备的`00 0A`
2. **透传模式敏感操作检查**：操作时机问题需要修复

## 修复内容

### 1. 滤波次数地址修复

#### 1.1 读取滤波次数地址修复
**位置**: `mainwindow.cpp:835-839`

**修改前**:
```cpp
QByteArray sendData = createModbusCommand(m_globalHeadStr + "03" + "00 0A 00 01");
```

**修改后**:
```cpp
// 1611A-HT型号使用不同的滤波次数地址
QString filterAddr = (deviceModel == "1611A-HT(PT100)" || deviceModel == "1611A-HT(PT1000)") ? "00 03" : "00 0A";
QByteArray sendData = createModbusCommand(m_globalHeadStr + "03" + filterAddr + " 00 01");
```

#### 1.2 写入滤波次数地址修复
**位置**: `mainwindow.cpp:1421-1423`

**修改前**:
```cpp
QByteArray sendData = createModbusCommand(m_globalHeadStr + "06" + "00 0A " + byteArray.toHex(' '));
```

**修改后**:
```cpp
// 1611A-HT型号使用不同的滤波次数地址
QString filterAddr = (deviceModel == "1611A-HT(PT100)" || deviceModel == "1611A-HT(PT1000)") ? "00 03" : "00 0A";
QByteArray sendData = createModbusCommand(m_globalHeadStr + "06" + filterAddr + " " + byteArray.toHex(' '));
```

### 2. 透传模式敏感操作检查修复

#### 2.1 设备型号切换时机修复
**问题**: 原来的检查在操作执行之后，无法阻止操作
**解决**: 改用`currentIndexChanged`信号，在操作前检查

**修改前**:
```cpp
connect(ui->deviceModel_Cal, &QComboBox::currentTextChanged, this, &MainWindow::updateChannelNums_Cal);
```

**修改后**:
```cpp
connect(ui->deviceModel_Cal, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &MainWindow::onDeviceModelChanged);
```

#### 2.2 新增设备型号切换检查函数
**位置**: `mainwindow.cpp:4511-4536`

```cpp
void MainWindow::onDeviceModelChanged(int index)
{
    static int previousIndex = -1;
    
    // 如果是初始化调用，直接执行
    if (previousIndex == -1) {
        previousIndex = index;
        updateChannelNums_Cal(ui->deviceModel_Cal->itemText(index));
        return;
    }
    
    // 检查透传模式状态，如果处于透传模式则提示用户
    if (!checkTransparentModeBeforeOperation("切换设备型号")) {
        // 用户选择不继续，恢复之前的选择
        ui->deviceModel_Cal->blockSignals(true);
        ui->deviceModel_Cal->setCurrentIndex(previousIndex);
        ui->deviceModel_Cal->blockSignals(false);
        return;
    }
    
    // 更新previousIndex并执行切换
    previousIndex = index;
    updateChannelNums_Cal(ui->deviceModel_Cal->itemText(index));
}
```

#### 2.3 其他敏感操作检查
**连接设备**:
```cpp
void MainWindow::on_connectButton_Cal_clicked() {
    if (!checkTransparentModeBeforeOperation("连接设备")) {
        return;
    }
    // 继续连接逻辑...
}
```

**断开设备**:
```cpp
void MainWindow::on_disconnectButton_Cal_clicked() {
    if (!checkTransparentModeBeforeOperation("断开设备连接")) {
        return;
    }
    // 继续断开逻辑...
}
```

**通信类型切换**:
```cpp
void MainWindow::onCommunicationType_CalChanged(int index) {
    if (!checkTransparentModeBeforeOperation("切换通信类型")) {
        // 恢复之前的选择
        ui->communicationType_Cal->blockSignals(true);
        ui->communicationType_Cal->setCurrentIndex(index == 0 ? 1 : 0);
        ui->communicationType_Cal->blockSignals(false);
        return;
    }
    // 继续切换逻辑...
}
```

### 3. 1611A-HT型号配置总览

#### 3.1 设备特性
- **透传模式设备**：需要特殊的唤醒和退出流程
- **15通道设备**：支持15个测量通道
- **特殊地址配置**：
  - 写入地址：`0x0152`
  - 读取地址：`0x0034`
  - 滤波次数地址：`0x0003`（而不是普通设备的`0x000A`）

#### 3.2 支持的操作
- ✅ 序列号读写（透传模式下）
- ✅ 滤波次数读写（使用特殊地址`00 03`）
- ✅ 批量校准（15通道分批处理）
- ❌ 复位功能（1611A-HT无复位）
- ❌ 波特率读写（透传模式不需要）

#### 3.3 UI界面配置
- 启用透传模式按钮
- 禁用复位按钮
- 启用滤波次数读写（使用特殊地址）
- 隐藏板卡相关组件

### 4. 修复效果

#### 4.1 滤波次数操作
**修复前**：
- 1611A-HT使用错误地址`00 0A`，导致读写失败

**修复后**：
- 1611A-HT使用正确地址`00 03`，读写成功
- 其他设备继续使用`00 0A`地址

#### 4.2 透传模式操作安全性
**修复前**：
- 敏感操作在执行后才检查，无法阻止
- 用户可能意外重置透传状态

**修复后**：
- 所有敏感操作在执行前检查透传状态
- 用户有明确的选择权
- 支持操作取消和状态恢复

### 5. 技术细节

#### 5.1 地址映射
```cpp
// 普通设备滤波次数地址
"00 0A"  // 读取：01 03 00 0A 00 01
         // 写入：01 06 00 0A XX XX

// 1611A-HT滤波次数地址  
"00 03"  // 读取：01 03 00 03 00 01
         // 写入：01 06 00 03 XX XX
```

#### 5.2 信号阻塞机制
使用`blockSignals()`防止递归调用：
```cpp
ui->deviceModel_Cal->blockSignals(true);
ui->deviceModel_Cal->setCurrentIndex(previousIndex);
ui->deviceModel_Cal->blockSignals(false);
```

#### 5.3 静态变量保存状态
使用静态变量保存之前的选择：
```cpp
static int previousIndex = -1;
```

这样的修复确保了1611A-HT型号设备的所有功能都能正常工作，同时提高了透传模式操作的安全性。
