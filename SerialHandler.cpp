﻿// SerialHandlerPro.cpp
#include "SerialHandler.h"
#include "SerialWorker.h"
#include "DataProcessor.h"
#include <QEventLoop>
#include <QDebug>

SerialHandlerPro::SerialHandlerPro(QObject *parent) : QObject(parent)
{
    m_worker = new SerialWorker;
    m_thread = new QThread(this);
    m_worker->moveToThread(m_thread);

    // 建立线程与worker的生命周期关系
    connect(m_thread, &QThread::finished, m_worker, &QObject::deleteLater);

    connect(this, &SerialHandlerPro::writeDataSignal, m_worker, &SerialWorker::writeData);
    connect(this, &SerialHandlerPro::openPortSignal, m_worker, &SerialWorker::openPort);
    connect(this, &SerialHandlerPro::closePortSignal, m_worker, &SerialWorker::closePort);

    connect(m_worker, &SerialWorker::operationCompleted,
            this, &SerialHandlerPro::onOperationCompleted);

    m_thread->start();
}

SerialHandlerPro::~SerialHandlerPro()
{
    // emit closePortSignal();
    closePort();
    // m_thread->quit();
    // m_thread->wait();

    if (m_thread)
    {
        m_thread->quit();
        if (!m_thread->wait(500))
        {
            m_thread->terminate();
            m_thread->wait();
        }
    }

    m_worker = nullptr;
}

// QPair<bool, QString> SerialHandlerPro::writeData(const QByteArray &data, const QString &command)
//{
//     QMutexLocker locker(&m_mutex);
//     emit writeDataSignal(data, command);

//    QEventLoop loop;
//    connect(m_worker, &SerialWorker::operationCompleted, &loop, &QEventLoop::quit);
//    loop.exec();

//    return qMakePair(m_operationSuccess, m_operationResult);
//}

QPair<bool, QString> SerialHandlerPro::writeData(const QByteArray &data, const QString &command)
{
    QMutexLocker locker(&m_mutex);

    // Reset a flag to track when result is updated
    m_resultReady = false;

    emit writeDataSignal(data, command);

    QEventLoop loop;

    // Use the resultReady signal instead of operationCompleted
    connect(this, &SerialHandlerPro::resultReady, &loop, &QEventLoop::quit);

    // Set timeout to prevent infinite wait
    QTimer timer;
    timer.setSingleShot(true);
    connect(&timer, &QTimer::timeout, &loop, &QEventLoop::quit);
    timer.start(6000); // 6 seconds timeout (longer than the SerialWorker's 5 seconds)

    loop.exec();

    // Check if we timed out
    if (!m_resultReady)
    {
        // return qMakePair(false, "Operation timeout");
        return QPair<bool, QString>(false, "Operation timeout");
    }

    return qMakePair(m_operationSuccess, m_operationResult);
}

bool SerialHandlerPro::openPort(const QString &portName, int baudRate)
{
    QMutexLocker locker(&m_mutex);
    emit openPortSignal(portName, baudRate);

    QEventLoop loop;
    connect(m_worker, &SerialWorker::operationCompleted, &loop, &QEventLoop::quit);
    loop.exec();

    return m_operationSuccess;
}

bool SerialHandlerPro::closePort()
{
    QMutexLocker locker(&m_mutex);
    emit closePortSignal();

    QEventLoop loop;
    connect(m_worker, &SerialWorker::operationCompleted, &loop, &QEventLoop::quit);
    loop.exec();

    return m_operationSuccess;
}

bool SerialHandlerPro::isPortOpen() const
{
    return m_worker->isPortOpen();
}

void SerialHandlerPro::registerProcessor(DataProcessor *processor)
{
    // QMetaObject::invokeMethod(m_worker, [this, processor]() {
    //     m_worker->registerProcessor(processor);
    // });
    // 改为同步调用，确保立即完成
    QMetaObject::invokeMethod(m_worker, [processor, this]()
                              { m_worker->registerProcessor(processor); }, Qt::BlockingQueuedConnection); // 使用阻塞方式
}

// void SerialHandlerPro::onOperationCompleted(bool success, const QString &result, const QString &command)
//{
//     m_operationSuccess = success;
//     m_operationResult = result;
//     emit operationResult(success, result);
// }

void SerialHandlerPro::onOperationCompleted(bool success, const QString &result, const QString &command)
{
    m_operationSuccess = success;
    m_operationResult = result;
    m_resultReady = true; // Set flag indicating result is ready

    emit operationResult(success, result);
    emit resultReady(); // Signal to quit the event loop
}

// #include "SerialHandler.h"
// #include <QEventLoop>
// #include <QDebug>

////-----------------------------------------------------------------------------
//// DataProcessor implementations
//// ZCLOG334NTC设备处理器示例
// class ZCLOG334NTCProcessor : public DataProcessor {
// public:
//     explicit ZCLOG334NTCProcessor(QObject* parent = nullptr) : DataProcessor(parent) {}

//    bool supportsCommand(const QString& command) const override {
//        return command.startsWith("ZCLOG334NTC_");  // 支持所有以该前缀开头的命令
//    }

//    void processData(const QByteArray& data, const QString& command) override {
//        if (command == "ZCLOG334NTC_EndCal") {
//            if (data.size() >= 5) {
//                QString hexData = data.toHex(' ');
//                QStringList hexList = hexData.split(" ");
//                QString data1 = hexList.value(4, "").toUpper();
//                emit resultReady(true, data1 == "4C" ? "success" : "fail", command);
//            } else {
//                emit resultReady(false, "fail", command);
//            }
//        }
//    }
//};

////-----------------------------------------------------------------------------
//// SerialWorker implementation
// SerialWorker::SerialWorker(QObject* parent)
//     : QObject(parent),
//     m_serialPort(new QSerialPort(this)),
//     m_receiveDelayTimer(new QTimer(this)),
//     isSending(false),
//     m_timeoutTimer(new QTimer(this)) {
//     connect(m_serialPort, &QSerialPort::readyRead, this, &SerialWorker::startReceiveTimer);
//     m_receiveDelayTimer->setSingleShot(true);
//     connect(m_receiveDelayTimer, &QTimer::timeout, this, &SerialWorker::doDataReciveWork);

//    m_timeoutTimer->setSingleShot(true);
//    connect(m_timeoutTimer, &QTimer::timeout, this, &SerialWorker::onTimeout);

//    // 注册默认处理器
//    registerProcessor(new ZCLOG334NTCProcessor(this));
//}

// SerialWorker::~SerialWorker() {
//     closePort();
//     qDeleteAll(m_processors);
//     delete m_serialPort;
// }

// void SerialWorker::registerProcessor(DataProcessor* processor) {
//     QMutexLocker locker(&m_mutex);
//     if (!m_processors.contains(processor)) {
//         m_processors.append(processor);
//         connect(processor, &DataProcessor::resultReady, this, &SerialWorker::sendResultToGui);
//     }
// }

// bool SerialWorker::isPortOpen() const
//{
//     return m_serialPort->isOpen();
// }

// void SerialWorker::openPort(const QString &portName, int baudRate)
//{
//     // 串口操作不需要频繁加锁，锁定仅用于防止多线程操作串口
//     QMutexLocker locker(&m_mutex);

//    if (m_serialPort->isOpen()) {
//        m_serialPort->close();
//    }

//    m_serialPort->setPortName(portName);
//    m_serialPort->setBaudRate(baudRate);
//    m_serialPort->setDataBits(QSerialPort::Data8);
//    m_serialPort->setParity(QSerialPort::NoParity);
//    m_serialPort->setStopBits(QSerialPort::OneStop);
//    m_serialPort->setFlowControl(QSerialPort::NoFlowControl);

//    if (m_serialPort->open(QIODevice::ReadWrite)) {
//        emit operationResult(true, "串口打开成功");
//    } else {
//        emit operationResult(false, "无法打开串口: " + m_serialPort->errorString());
//    }
//}

// void SerialWorker::closePort()
//{
//     QMutexLocker locker(&m_mutex);

//    if (m_serialPort->isOpen()) {
//        m_serialPort->close();
//        emit operationResult(true, "串口已关闭"); //虽然关闭成功，但是要为false，让打开按钮可用
//    }
//}

// void SerialWorker::writeData(const QByteArray& data, const QString& command) {
//     QMutexLocker locker(&m_mutex);
//     if (isSending) QThread::msleep(500);

//    isSending = true;
//    m_currentCommand = command;
//    m_serialPort->write(data);
//    isCommandTimeout = false;        // 重置超时标志
//    m_timeoutTimer->start(5000);
//    isSending = false;
//}

// void SerialWorker::startReceiveTimer()
//{
//     if (!m_receiveDelayTimer->isActive()) {
//         m_receiveDelayTimer->start(100);
//     }
// }

// void SerialWorker::doDataReciveWork()
//{
//     QMutexLocker locker(&m_mutex);
//     QByteArray newData = m_serialPort->readAll(); // 读取最新接收到的数据
//     m_receivedData.append(newData); // 将最新数据添加到已接收的数据中
//     qDebug() <<"m_receivedData: "<< m_receivedData;

//    processCommand(m_receivedData);
//}

// void SerialWorker::processCommand(const QByteArray& data) {
//     //qDebug() << "m_currentCommand:" << m_currentCommand;
//     //qDebug() << "isCommandTimeout:" << isCommandTimeout;

//    if (!m_currentCommand.isEmpty() && !isCommandTimeout) {
//        // 处理命令
//        for (DataProcessor* processor : m_processors) {
//            // qDebug() << "processor: " << processor;
//            if (processor->supportsCommand(m_currentCommand)) {
//                processor->processData(data, m_currentCommand);
//                //if (m_timeoutTimer->isActive()) {
//                //    m_timeoutTimer->stop(); // Stop timer since data was processed
//                //}
//            }
//        }
//        // 清理数据和状态
//        m_serialPort->clear();
//        m_receivedData.clear();
//        m_currentCommand.clear();
//    } else if (isCommandTimeout) {
//        // 忽略超时命令的迟到数据
//        qDebug() << "忽略超时命令的迟到响应";
//    }
//}

// void SerialWorker::onTimeout() {
//     QMutexLocker locker(&m_mutex);
//     if (!m_currentCommand.isEmpty()) {
//         isCommandTimeout = true;
//         emit sendResultToGui(false, "timeout", m_currentCommand);
//         m_currentCommand.clear();
//         m_serialPort->clear();
//         m_receivedData.clear();
//     }
// }

// void SerialWorker::readData()
//{
//     m_receivedData.append(m_serialPort->readAll());
//     startReceiveTimer();
// }

////-----------------------------------------------------------------------------
//// SerialHandlerPro implementation
// SerialHandlerPro::SerialHandlerPro(QObject* parent) : QObject(parent) {
//     m_worker = new SerialWorker;
//     m_thread = new QThread(this);
//     m_worker->moveToThread(m_thread);

//    // 连接信号槽
//    connect(this, &SerialHandlerPro::openPortSignal, m_worker, &SerialWorker::openPort);
//    connect(this, &SerialHandlerPro::closePortSignal, m_worker, &SerialWorker::closePort);
//    connect(this, &SerialHandlerPro::writeDataSignal, m_worker, &SerialWorker::writeData);
//    connect(m_worker, &SerialWorker::operationResult, this, &SerialHandlerPro::onOperationResult);
//    connect(m_worker, &SerialWorker::sendResultToGui, this, &SerialHandlerPro::onSendResultToGuiSlots);

//    m_thread->start();
//}

// SerialHandlerPro::~SerialHandlerPro()
//{
//     // 关闭串口并停止连续读取
//     emit closePortSignal();
//     emit stopContinuousReadSignal();

//    m_thread->quit();
//    m_thread->wait();

//    // 安全销毁工作对象
//    m_worker->deleteLater();
//}

// void SerialHandlerPro::registerProcessor(DataProcessor* processor) {
//     QMetaObject::invokeMethod(m_worker, [this, processor]() {
//         m_worker->registerProcessor(processor);
//     });
// }

// bool SerialHandlerPro::isPortOpen() const
//{
//     return m_worker->isPortOpen();
// }

// void SerialHandlerPro::openPort(const QString &portName, int baudRate)
//{
//     emit openPortSignal(portName, baudRate);
// }

// void SerialHandlerPro::closePort()
//{
//     emit closePortSignal();
// }

// void SerialHandlerPro::writeData(const QByteArray &data, const QString &command)
//{
//     emit writeDataSignal(data, command);
// }

// void SerialHandlerPro::stopContinuousRead()
//{
//     emit stopContinuousReadSignal();
// }

///*发送给主线程*/
// void SerialHandlerPro::onOperationResult(bool success, const QString &message)
//{
//     emit operationResult(success, message);
// }

//// 未使用
// void SerialHandlerPro::onDataReceived(const QByteArray &data, const QString &command)
//{
//     emit dataReceived(data, command);
// }

// void SerialHandlerPro::onSendResultToGuiSlots(bool status, const QString &result, const QString &isThis)
//{
//     //emit dataReceived(data, command);
//     emit onSendResultToGui(status, result, isThis);
// }
