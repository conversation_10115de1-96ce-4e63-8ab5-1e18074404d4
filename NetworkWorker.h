﻿// NetworkWorker.h
#pragma once

#include <QObject>
#include <QTcpSocket>
#include <QTimer>
#include <QMutex>
#include <QList>

class DataProcessor;

class NetworkWorker : public QObject
{
    Q_OBJECT
public:
    explicit NetworkWorker(QObject *parent = nullptr);
    ~NetworkWorker();

    bool isConnected() const;

public slots:
    void connectToHost(const QString &host, quint16 port);
    void disconnectFromHost();
    void writeData(const QByteArray &data, const QString &command);
    void registerProcessor(DataProcessor *processor);

signals:
    void operationCompleted(bool success, const QString &result, const QString &command);

private slots:
    void handleReadyRead();
    void handleTimeout();
    void processReceivedData();
    void handleSocketError(QAbstractSocket::SocketError error);

private:
    QTcpSocket *m_socket;
    QTimer *m_receiveTimer;
    QTimer *m_timeoutTimer;
    QMutex m_mutex;
    QByteArray m_receivedData;
    QString m_currentCommand;
    bool m_isTimeout;
    QList<DataProcessor *> m_processors;

    void clearBuffers();
    bool m_isWaitingResponse;
    void stopTimers();
};
